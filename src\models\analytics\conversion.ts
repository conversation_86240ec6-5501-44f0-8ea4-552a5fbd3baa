import mongoose, { Schema, Document } from 'mongoose';

interface IConversion extends Document {
    userId: mongoose.Types.ObjectId;
    conversionType:string;
}

const conversionSchema: Schema = new Schema({
    userId: { type: Schema.Types.ObjectId, ref: 'User', required: true },
    conversionType: { type: String, required: true },
},{ timestamps: true });
export default mongoose.model<IConversion>('Conversion', conversionSchema);
