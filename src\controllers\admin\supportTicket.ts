import { NextFunction, Request, Response } from "express";
import supportTicket from "../../models/admin/supportTicket";

export const createTicket = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<Response> => {
  try {
    const { title, description, userId, priority, ip } = req.body;

    const newTicket = await supportTicket.create({
      userId,
      title,
      description,
      priority,
      ip,
    });

    return res.status(201).json({
      message: "Support ticket created successfully",
      ticket: newTicket,
    });
  } catch (error) {
    next(error);
    console.error("Error creating ticket:", error);
    return res
      .status(500)
      .json({ error: "Server error. Please try again later." });
  }
};

export const getTickets = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<Response> => {
  try {
    const tickets = await supportTicket.find().populate("userId", "first_name last_name email");
    return res.status(200).json({
      tickets
    });
  } catch (error) {
    next(error);
    console.error("Error fetching tickets:", error);
    return res
      .status(500)
      .json({ error: "Server error. Please try again later." });
  }
};

export const getTicketsbyId = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<Response> => {
  try {
    const { ticketId } = req.params;

    const ticket = await supportTicket
      .findById(ticketId)
      .populate("userId", "name email");

    if (!ticket) {
      return res.status(404).json({ error: "Ticket not found" });
    }

    return res.status(200).json({
      ticket
    });
  } catch (error) {
    next(error);
    console.error("Error fetching ticket:", error);
    return res
      .status(500)
      .json({ error: "Server error. Please try again later." });
  }
};

export const updateTicketStatus = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<Response> => {
  try {
    const { ticketId, status } = req.body;

    const updatedTicket = await supportTicket.findByIdAndUpdate(
      ticketId,
      { status },
      { new: true }
    );

    if (!updatedTicket) {
      return res.status(404).json({ error: "Ticket not found" });
    }

    return res.status(200).json({
      message: "Ticket status updated successfully",
      ticket: updatedTicket,
    });
  } catch (error) {
    next(error);
    console.error("Error updating ticket status:", error);
    return res.status(500).json({ error: "Unable to update ticket status." });
  }
};

export const deleteTicket = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<Response> => {
  try {
    const { ticketId } = req.params;

    const deletedTicket = await supportTicket.findByIdAndDelete(ticketId);

    if (!deletedTicket) {
      return res.status(404).json({ error: "Ticket not found" });
    }

    return res.status(200).json({
      message: "Ticket deleted successfully",
      ticket: deletedTicket,
    });
  } catch (error) {
    next(error);
    console.error("Error deleting ticket:", error);
    return res.status(500).json({ error: "Unable to delete ticket." });
  }
};
