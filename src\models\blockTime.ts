import mongoose, { Document, Schema } from 'mongoose';

export interface IBlockedTime extends Document {
    userId: mongoose.Types.ObjectId;  
    calendarId: mongoose.Types.ObjectId;
    startTime: string; // HH:MM
    endTime: string;   // HH:MM
    notes?: string;  // Notes for the manager
    startDate: Date; // Start date for blocking
    endDate: Date;   // End date for blocking
}

const blockedTimeSchema: Schema<IBlockedTime> = new Schema({
    userId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User', 
        required: true,
    },    
    calendarId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'CalendarSettings', 
        required: true,
    },
    startTime: {
        type:String ,
        required: true,
        validate: {
            validator: function(v: string) {
                return /^([01]\d|2[0-3]):([0-5]\d)$/.test(v); // Matches HH:MM format
            },
            message: 'Invalid time format. Expected HH:MM.'
        }
    },
    endTime: {
        type: String,
        required: true,
        validate: {
            validator: function(v: string) {
                return /^([01]\d|2[0-3]):([0-5]\d)$/.test(v); // Matches HH:MM format
            },
            message: 'Invalid time format. Expected HH:MM.'
        }
    },
    startDate: {
        type: Date,
        required: true,
    },
    endDate: {
        type: Date,
        required: true,
        validate: {
            validator: function (this: IBlockedTime) {
                return this.endDate >= this.startDate; // End date must be after or equal to start date
            },
            message: 'End date must be after the start date.'
    }
    }
},{ timestamps: true });

export default mongoose.model<IBlockedTime>('BlockTime', blockedTimeSchema);
