import {  Response, NextFunction } from 'express';
import elementInteraction from '../../models/analytics/elementInteraction';

export const trackElementInteraction = async (req: any, res: Response, next: NextFunction): Promise<Response> => {
    const { elementId,actionType } = req.body;
    const userId = req.user._id
    try {
        const newInteraction =  new elementInteraction({
            userId,
            actionType,
            elementId
        })
        await newInteraction.save()
        return res.status(201).json({
            success:true,
            message:"Interaction Added"
        })
    } catch (error) {
        console.error('Error tracking interaction:', error);
        next(error); 
        return res.status(500).json({ success: false, error: 'Server error' });
    }
}