import express from 'express';

import { createPuzzleProduct, deletePuzzleProduct, getAllPuzzleProducts, getPuzzleProductById, updatePuzzleProduct } from '../../controllers/admin/puzzleProduct';
import { createPuzzlePrice, deletePuzzlePrice, getAllPuzzlePrice, getPuzzlePriceById, updatePuzzlePrice } from '../../controllers/admin/puzzlePrice';
import { createPuzzleCredit , deletePuzzleCreditById, getAllCreditPackages, getPuzzleCreditById, updatePuzzleCreditById} from '../../controllers/admin/puzzleCredit';
import { createBlog, createTag, deleteBlog, deleteTag, getBlogbyId, getBlogs, getTags, updateBlog } from '../../controllers/admin/blog';
import { blogImageUpload } from '../../utils/cloudinary';
import { authorizeRoles, authMiddleware } from '../../middleware/authService';
import { addUser, deleteUser, editUser, getUsers, toggleUserStatus } from '../../controllers/admin/admin';
import { getUserLogs, getUserswithLogs } from '../../controllers/admin/userLogs';
import { createFaq, deleteFaq, getAllFaqs, getFaqById, updateFaq } from '../../controllers/admin/faqs';
import { createTicket, deleteTicket, getTickets, getTicketsbyId } from '../../controllers/admin/supportTicket';
import { createPromoCode, deletePromoCode, getPromoCodeById, getPromoCodes, updatePromoCode } from '../../controllers/admin/puzzlePromoCode';
import { getMaintenanceMode, setMaintenanceMode } from '../../controllers/admin/maintenanceMode';
import { RegisterAdmin } from '../../controllers/admin/auth';
const router = express.Router();

//Dont delete
router.post('/admin/create', RegisterAdmin)


//Users
router.post('/admin/user', authMiddleware, authorizeRoles(['admin']),addUser);
router.post('/admin/user/edit', authMiddleware, authorizeRoles(['admin']), editUser)
router.get('/admin/user', authMiddleware, authorizeRoles(['admin']),getUsers);
router.delete('/admin/user/:userId', authMiddleware, authorizeRoles(['admin']), deleteUser);
// router.put('/reset-password/:userId', authMiddleware, autho\rizeRoles(['admin']), resetPassword);
router.patch('/admin/toggle-status/:userId', authMiddleware, authorizeRoles(['admin']), toggleUserStatus);

//Userlogs
router.get("/admin/logs/users",  authMiddleware, authorizeRoles(['admin']),getUserswithLogs);
router.get("/admin/logs/:userId", authMiddleware, authorizeRoles(['admin']), getUserLogs);

//FAQS
router.get('/admin/faq', getAllFaqs); 
router.get('/admin/faq/:Id',authMiddleware, authorizeRoles(['admin']), getFaqById); 
router.post('/admin/faq',authMiddleware, authorizeRoles(['admin']), createFaq); 
router.put('/admin/faq/:Id',authMiddleware, authorizeRoles(['admin']), updateFaq); 
router.delete('/admin/faq/:Id',authMiddleware, authorizeRoles(['admin']), deleteFaq); 

//Puzzle Subscription Products
router.post('/admin/products',authMiddleware, authorizeRoles(['admin']),createPuzzleProduct);
router.get('/admin/products',authMiddleware, authorizeRoles(['admin']), getAllPuzzleProducts);
router.get('/admin/products/:Id',authMiddleware, authorizeRoles(['admin']), getPuzzleProductById);
router.put('/admin/products/:Id', authMiddleware, authorizeRoles(['admin']),updatePuzzleProduct);
router.delete('/admin/products/:Id', authMiddleware, authorizeRoles(['admin']),deletePuzzleProduct);

//Puzzle Subscription Prices
router.post('/admin/price',authMiddleware, authorizeRoles(['admin']), createPuzzlePrice);
router.get('/admin/price',authMiddleware, authorizeRoles(['admin']), getAllPuzzlePrice);
router.get('/admin/price/:Id', authMiddleware, authorizeRoles(['admin']),getPuzzlePriceById);
router.put('/admin/price/:Id', authMiddleware, authorizeRoles(['admin']),updatePuzzlePrice);
router.delete('/admin/price/:Id', authMiddleware, authorizeRoles(['admin']),deletePuzzlePrice);

//Puzzle Credits
router.post('/admin/credit',authMiddleware, authorizeRoles(['admin']), createPuzzleCredit);
router.get('/admin/credit',authMiddleware, getAllCreditPackages);
router.get('/admin/credit/:Id',authMiddleware, authorizeRoles(['admin']), getPuzzleCreditById);
router.put('/admin/credit/:Id',authMiddleware, authorizeRoles(['admin']), updatePuzzleCreditById);
router.delete('/admin/credit/:Id',authMiddleware, authorizeRoles(['admin']), deletePuzzleCreditById);

//Blogs
router.post('/admin/blogs',blogImageUpload.single('image'),createBlog);
router.post('/admin/tags', createTag);
router.put('/admin/blogs/:Id',blogImageUpload.single('image'),updateBlog);
router.get('/admin/blogs/:Id',getBlogbyId);
router.get('/admin/blogs',getBlogs)
router.get('/admin/tags', getTags);
router.delete('/admin/blogs/:Id',deleteBlog);
router.delete('/admin/tag/:Id',deleteTag);

//Support Tickets
router.post('/support-tickets', authMiddleware, createTicket);
router.get('/admin/tickets',authMiddleware, authorizeRoles(['admin']),getTickets);
router.get('/admin/tickets/:ticketId',authMiddleware, authorizeRoles(['admin']),getTicketsbyId);
router.delete('/admin/tickets/:ticketId',authMiddleware, authorizeRoles(['admin']),deleteTicket);
router.patch('/admin/tickets/update',authMiddleware, authorizeRoles(['admin']),getTickets);


//PuzzlePromoCode
router.post('/admin/promo-code',authMiddleware, authorizeRoles(['admin']), createPromoCode);
router.get('/admin/promo-code', authMiddleware, authorizeRoles(['admin']),getPromoCodes);
router.get('/admin/promo-code/:Id', authMiddleware, authorizeRoles(['admin']),getPromoCodeById);
router.put('/admin/promo-code/:Id', authMiddleware, authorizeRoles(['admin']),updatePromoCode);
router.delete('/admin/promo-code/:Id',authMiddleware, authorizeRoles(['admin']), deletePromoCode);

//Maintenance
router.get('/maintenance',getMaintenanceMode)
router.put('/maintenance',authMiddleware, authorizeRoles(['admin']),setMaintenanceMode);


export default router;
