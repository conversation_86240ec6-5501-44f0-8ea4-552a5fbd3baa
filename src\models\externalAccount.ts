import mongoose, { Schema, Document } from 'mongoose';

interface IAppleCredentials {
  username:string;
  appPassword:string;
  calendarUrl:string;
}
export interface IExternalAccount extends Document {
  userId: mongoose.Types.ObjectId; 
  name: 'google' | 'outlook' | 'stripe';      
  connect: boolean;                 
  activate: boolean;
  connectedEmail?:string;               
  accessToken?: string;            
  refreshToken?: string; 
  tokenExpiresAt?: Date;
  appleCredentials?:IAppleCredentials;          
}

const externalAccountSchema: Schema = new Schema({
  userId: { type: mongoose.Types.ObjectId, required: true, ref: 'User' },
  name: { type: String, enum: ['google', 'outlook', 'stripe'], required: true },
  connect: { type: Boolean, default: false },
  connectedEmail:{type:String},
  activate: { type: Boolean, default: false },
  accessToken: { type: String },  
  refreshToken: { type: String }, 
  tokenExpiresAt: { type: Date },
  appleCredentials: {
    username: { type: String }, 
    appPassword: { type: String },
    calendarUrl: { type: String }, 
  }
}, { timestamps: true });


export default mongoose.model<IExternalAccount>('ExternalAccount', externalAccountSchema);
