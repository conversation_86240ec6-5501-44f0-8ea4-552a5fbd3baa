import { google } from 'googleapis';
import dotenv from 'dotenv';
import axios from 'axios';
dotenv.config();
import { AuthorizationCodeRequest, AuthorizationUrlRequest, ConfidentialClientApplication, RefreshTokenRequest } from '@azure/msal-node';
import { IExternalAccount } from '../models/externalAccount';
import { stripeClient } from './stripesService';
// import { createAccount } from "dav";

const REDIRECT_URI = `${process.env.FRONTENDURL}/oauth/callback`
const googleOAuth2Client = new google.auth.OAuth2(
  process.env.GOOGLE_CLIENT_ID,
  process.env.GOOGLE_CLIENT_SECRET,
  REDIRECT_URI
);
if (!process.env.GOOGLE_CLIENT_ID || !process.env.GOOGLE_CLIENT_SECRET || !process.env.FRONTENDURL) {
  throw new Error('Missing necessary Google OAuth environment variables.');
}
if (!process.env.OUTLOOK_CLIENT_ID || !process.env.OUTLOOK_CLIENT_SECRET || !process.env.TENANT_ID) {
  throw new Error('Missing necessary Outlook OAuth environment variables.');
}
// if (!process.env.STRIPE_CLIENT_ID ) {
//   throw new Error('Missing necessary Stripe OAuth environment variables.');
// }
const clientId = process.env.OUTLOOK_CLIENT_ID!;
const clientSecret  = process.env.OUTLOOK_CLIENT_SECRET!;
const tenantId  = process.env.TENANT_ID!;
const msalConfig = {
  auth: {
    clientId,
    authority: `https://login.microsoftonline.com/${tenantId}`,
    clientSecret,
  },
};
const msalClient = new ConfidentialClientApplication(msalConfig);


export const getGoogleAuthUrl = (): string => {
  const scopes = [
    'https://www.googleapis.com/auth/calendar',
    'https://www.googleapis.com/auth/userinfo.profile',
    'https://www.googleapis.com/auth/userinfo.email',
  ];

  return googleOAuth2Client.generateAuthUrl({
    access_type: 'offline',  // To get a refresh token
    scope: scopes,
    state:'google',
    prompt: 'consent'
  });
};


export const getOutlookAuthUrl = async () => {
  const authCodeUrlParameters:AuthorizationUrlRequest= {
    scopes: ["offline_access", "Calendars.ReadWrite", "User.Read"],
    redirectUri: REDIRECT_URI,
    state:'outlook'
    
  };
  try{
   const response =  await msalClient.getAuthCodeUrl(authCodeUrlParameters)
   return response
  }catch(error){
    console.log(JSON.stringify(error))
    throw error
  }
};

const GOOGLE_PEOPLE_API_URL = 'https://www.googleapis.com/oauth2/v1/userinfo?alt=json';
const MICROSOFT_GRAPH_API_URL = 'https://graph.microsoft.com/v1.0/me';
const STRIPE_OAUTH_URL = 'https://connect.stripe.com/oauth/token';
export const getTokensFromCode = async (code: string, service: string) => {
  console.log("service:",service)
  if (service === 'google') {
    try {
      // Get tokens from Google
      const { tokens } = await googleOAuth2Client.getToken(code);

      const userProfileResponse = await axios.get(GOOGLE_PEOPLE_API_URL, {
        headers: {
          Authorization: `Bearer ${tokens.access_token}`,
        },
      });

      const email = userProfileResponse.data.email;
      return {
        accessToken: tokens.access_token,
        refreshToken: tokens.refresh_token,
        expiresAt: new Date(tokens?.expiry_date!),
        email,
      };

    } catch (error) {
      console.error('Error generating Google token or fetching user email:', error);
      throw new Error('Failed to generate Google token');
    }
  }

  if (service === 'outlook') {
    try {
      const tokenRequest: AuthorizationCodeRequest = {
        code: code,
        scopes: ["Calendars.ReadWrite", "User.Read", "offline_access"],
        redirectUri: REDIRECT_URI,
      };
      const response = await msalClient.acquireTokenByCode(tokenRequest);
      // const { accessToken, refreshToken, account } = response;
      console.log("outlook response", response)
      const email = response.account?.name || response.account?.username
      return {
        accessToken: response.accessToken,
        refreshToken: response.idToken,
        expiresAt: response.expiresOn, 
        email
      };

    } catch (error) {
      console.error('Error generating Outlook token or fetching user email:', JSON.stringify(error));
      throw new Error('Failed to generate Outlook token');
    }
  }
  if (service === 'stripe'){
    try {
      const response = await stripeClient.oauth.token({
        grant_type: 'authorization_code',
        code: code as string,
      });
      // const response = await axios.post(STRIPE_OAUTH_URL, null, {
      //   params: {
      //     client_secret: process.env.STRIPE_KEY,
      //     code,
      //     grant_type: 'authorization_code',
      //   },
      // });
      const { access_token, refresh_token, stripe_user_id } = response;
      console.log(response)
      return {
        accessToken: access_token,
        refreshToken: refresh_token,
        // expiresAt:
        email:stripe_user_id,
      };
    } catch (error:any) {
      console.error('Error generating stripe token or fetching user email:', JSON.stringify(error));
      throw new Error('Failed to generate Stripe token');
    }
  }
  throw new Error('Unsupported service');
};

export const refreshAccessToken = async (ExternalAccount:IExternalAccount, service: 'google' | 'outlook') => {
  try {
    const currentTime = new Date();
    if (ExternalAccount.tokenExpiresAt && ExternalAccount.tokenExpiresAt > currentTime) {
      console.log(
        "Access Token Has not Expire"
      )
      return ExternalAccount.accessToken;
    }
    console.log(
      "Access Token Has Expired"
    )
    if (service === 'google') {
      googleOAuth2Client.setCredentials({access_token:ExternalAccount?.accessToken,refresh_token:ExternalAccount.refreshToken});
      const response = await googleOAuth2Client.refreshAccessToken()
      if(response?.credentials){
        ExternalAccount.accessToken = response?.credentials.access_token!
        ExternalAccount.refreshToken = response?.credentials.refresh_token!
        ExternalAccount.tokenExpiresAt= new Date(response?.credentials.expiry_date!)
        await ExternalAccount.save();
        return ExternalAccount.accessToken
      }      
    } else if (service === 'outlook') {
      const tokenRequest:RefreshTokenRequest = {
        refreshToken: ExternalAccount.refreshToken!,
        scopes: ["Calendars.ReadWrite", "User.Read", "offline_access"],
        redirectUri:REDIRECT_URI,
      };
      const response = await msalClient.acquireTokenByRefreshToken(tokenRequest);
      if (response?.accessToken) {
        ExternalAccount.accessToken = response.accessToken;
        ExternalAccount.tokenExpiresAt = new Date(Date.now() + (Number(response?.expiresOn) || 0) * 1000);
        await ExternalAccount.save();
        return ExternalAccount.accessToken;
      }
      // if(response?.refreshOn){
      //   ExternalAccount.refreshToken = response.refreshOn;
      // }
      
    }
   

  } catch (error) {
    console.error(`Error refreshing access token for service: ${service}`, error);
    throw error;
  }
};

const serverUrl = "https://caldav.icloud.com";
export const createAppleAccount =async(username:string, apppassword:string) =>{
  try {
    // const account = await createAccount({
    //   accountType:'caldav',
      

    //   server: serverUrl,
    //   credentials: { username, password: apppassword }
    // });

    // const calendar = account.calendars[0];
    // return { calendarUrl: calendar.url };
    return { calendarUrl:"" };
  } catch (error) {
    console.error(error)
    throw new Error('Failed to Authenticate Apple crendentials');

  }
}

