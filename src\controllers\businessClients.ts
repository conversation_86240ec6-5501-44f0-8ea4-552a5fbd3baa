import { NextFunction, Response } from "express";
import businessClient from "../models/businessClient";

interface clientDataType {
    clientName:string,
    clientEmail:string,
    phoneNumber:string,
    lastAppointmentDate:Date
}
export async function createClientForUser(userId:string, clientData:clientDataType) {
    try {
      const existingClient = await businessClient.findOne({
        userId: userId,
        clientEmail: clientData.clientEmail,
        phoneNumber:clientData.phoneNumber,

      });
      if (existingClient) {
        existingClient.lastAppointmentDate =clientData.lastAppointmentDate ; 
        existingClient.totalAppointments +=1
        await existingClient.save();
        console.log(`Updated lastAppointmentDate for client: ${existingClient.clientName}`);
        return existingClient;
      }else{
      const newClient = new businessClient({
        userId: userId,
        totalAppointments:1,
        ...clientData
      });
      await newClient.save();
      return newClient;
     }
    } catch (error) {
      console.error('Error creating client:', error);
    //   throw error
    }
}

export const createBusinessClients = async (req: any, res: Response, next:NextFunction):Promise<Response> => {
  try {
    const userId = req.user?._id
    const {clientEmail,phoneNumber,clientName} = req.body
    const existingClient = await businessClient.findOne({
      userId: userId,
      clientEmail:clientEmail,
      phoneNumber:phoneNumber,

    });
    if (existingClient) {
      return res.status(400).json({
        success:false, 
        error:"Client already exist"
      })
    }
    const newClient = new businessClient({
      userId: userId,
      clientName,
      clientEmail,
      phoneNumber
    });
    await newClient.save();
    return res.status(201).json({
      success:true,
      message:"Client is created successfully"
    })
  } catch (error) {
   next(error)
    console.log("Error creating clients", error)
    return res.status(500).json({ error: 'Error creating clients'});
  }
}


export const getBusinessClients = async (req: any, res: Response, next:NextFunction):Promise<Response> => {
    const userId = req.user?._id
    try {
      const clients = await businessClient.find({userId});
      return res.status(200).json(clients);
    } catch (error) {
      next(error)
      console.log("Error fetching clients", error)
      return res.status(500).json({ error: 'Error fetching clients'});
    }
  }
  
  export const getBusinessClientsbyId = async (req: any, res: Response, next:NextFunction):Promise<Response> => {
      const Id =req.params.Id
      const userId = req.user?._id
    try {
      const client = await businessClient.findOne({_id:Id, userId});
      if (!client) {
        return res.status(404).json({ error: 'Client not found' });
      }
      return res.status(200).json(client);
    } catch (error) {
      next(error)
      console.log("Error fetching client", error)
      return res.status(500).json({ error: 'Error fetching client'})    
    }
  }

  export const updateBusinessClient = async (req: any, res: Response, next:NextFunction):Promise<Response> => {
    const Id =req.params.Id
    const userId = req.user?._id
    try {
      const updatedData = req.body;
      const client = await businessClient.findOneAndUpdate({_id:Id, userId}, updatedData, { new: true });
      if (!client) {
        return res.status(404).json({ error: 'Client not found' });
      }
      return res.status(200).json({
        message:"Client details updated successfully",
        client
      });
    } catch (error) {
      next(error)
      console.log("Error updating client", error)
      return res.status(500).json({ error: 'Error updating client'})    }
  }

  export const DeleteBusinessClient = async (req: any, res: Response, next:NextFunction):Promise<Response> => {
    const Id =req.params.Id
    const userId = req.user?._id
    try {
      const client = await businessClient.findOneAndDelete({_id:Id, userId});
      if (!client) {
        return res.status(404).json({ error: 'Client not found' });
      }
      return res.status(200).json({ message: 'Client deleted successfully' });
    } catch (error) {
      next(error)
      console.log("Error updating client", error)
      return res.status(500).json({ error: 'Error updating client'})    }
  }

  // export const getBusinessClientsbyOneAppointment = async (req: any, res: Response, next:NextFunction):Promise<Response> => {
  //   const userId = req.user?._id
  //   try {
  //     const clients = await businessClient.find({userId});
  //     return res.status(200).json(clients);
  //   } catch (error) {
  //     next(error)
  //     console.log("Error fetching clients", error)
  //     return res.status(500).json({ error: 'Error fetching clients'});
  //   }
  // }
  

  