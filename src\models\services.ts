import mongoose, { Document, Schema } from 'mongoose';

interface IServiceOption extends Document {
  name: string;
  description?: string; 
  price: number; 
}
export interface IService extends Document {
  userId: mongoose.Types.ObjectId; // Reference to the user/business that owns the service
  name: string; // Name of the service
  description?: string; // Description of the service
  messageAfterScheduling?: string; // Message to show after scheduling
  duration: number; // Duration in minutes
  blockExtraTimeBefore?: number;
  blockExtraTimeAfter?: number;
  price: number; // Price of the service
  category: mongoose.Types.ObjectId; // Reference to the category of the service
  color?: string; // Optional color representation for the service
  picture?: string; // URL of the service picture
  access: 'public' | 'private'; // Access control for the service
  isGroupEvent?: boolean; // If the service allows group bookings
  directBookingLink?: string; // Direct booking link to skip service selection
  urlToken?: string;
  options?: IServiceOption[]; // Optional service options
}

const serviceSchema: Schema = new Schema({
  userId: { type: mongoose.Types.ObjectId, required: true, ref: 'User' },
  name: { type: String, required: true },
  description: { type: String },
  messageAfterScheduling: { type: String },
  duration: { type: Number, required: true },
  blockExtraTimeBefore: { type: Number },
  blockExtraTimeAfter: { type: Number },
  price: { type: Number, required: true, default: 0.00 },
  category: { type: mongoose.Types.ObjectId, ref: 'Category', required: true },
  color: { type: String },
  picture: { type: String },
  access: { type: String, enum: ['public', 'private'], required: true },
  isGroupEvent: { type: Boolean, default: false },
  directBookingLink: { type: String }, // Direct booking link
  urlToken: { type: String },
  options: [{
    name: { type: String, required: true },
    description: { type: String },
    price: { type: Number, required: true }
  }],

}, {
  timestamps: true // Automatically add createdAt and updatedAt fields
});

export default mongoose.model<IService>('Services', serviceSchema);
