<!DOCTYPE html>
<html>
<head>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 0;
      padding: 0;
      background-color: #f9f9f9;
    }
    .email-container {
      max-width: 600px;
      margin: 20px auto;
      background-color: #ffffff;
      border: 1px solid #e0e0e0;
      border-radius: 8px;
      overflow: hidden;
    }
    .email-header {
      background-color: #003366;
      color: #ffffff;
      padding: 20px;
      text-align: center;
      font-size: 24px;
      font-weight: bold;
    }
    .email-body {
      padding: 20px;
      color: #333333;
    }
    .email-body h2 {
      margin-top: 0;
      color: #003366;
    }
    .email-body p {
      line-height: 1.6;
      font-size: 16px;
      margin-bottom: 20px;
    }
    .email-footer {
      background-color: #f1f1f1;
      padding: 15px;
      text-align: center;
      font-size: 14px;
      color: #777777;
    }
    .email-footer a {
      color: #003366;
      text-decoration: none;
    }
    .cta-button {
      display: inline-block;
      background-color: #003366;
      color: #ffffff;
      text-decoration: none;
      padding: 12px 20px;
      font-size: 16px;
      border-radius: 5px;
      margin-top: 10px;
    }
    .cta-button:hover {
      background-color: #002244;
    }
  </style>
</head>
<body>
  <div class="email-container">
    <div class="email-header">
      New Booking Received!
    </div>
    <div class="email-body">
      <h2>Hi {{businessName}},</h2>
      <p>
        A new booking has been successfully made for your service, <strong>{{serviceName}}</strong>, on our platform.
      </p>
      <p>Here are the details of the booking:</p>
      <ul>
        <li><strong>Customer Name:</strong> {{userName}}</li>
        <li><strong>Service:</strong> {{serviceName}}</li>
        <li><strong>Date:</strong> {{date}}</li>
        <li><strong>Time:</strong> {{time}}</li>        
        <li><strong>Additional Notes:</strong> {{notes}}</li>
      </ul>
      <p>
        Please ensure that the necessary arrangements are made to provide an excellent experience for the customer. If you need to contact the customer, their details are available in your dashboard.
      </p>
      <p>
        <a href="{{dashboardLink}}" class="cta-button">View Booking Details</a>
      </p>
    </div>
    <div class="email-footer">
      Thank you for being part of  Puzzle Piece Solutions! We’re excited to support your business.
      <br><br>
              <p>&copy; 2024 PuzzlePiece Solutions. All Rights Reserved.</p>

      {{!-- <a href="{{contactLink}}">Contact Support</a> | <a href="{{termsLink}}">Terms & Conditions</a> --}}
    </div>
  </div>
</body>
</html>
