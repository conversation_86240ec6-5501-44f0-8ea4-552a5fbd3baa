import { Router } from 'express';
import { authMiddleware } from '../middleware/authService';
import { GetUserExternalAccounts, checkStripeConnected, connectExternalAccount, disconnectExternalAccount, handleOAuthCallback, syncSchedulesToExternalCalendars, toggleExternalAccountActivation } from '../controllers/externalAccount';
const router: Router = Router();

router.get('/auth/accounts/connect', authMiddleware, connectExternalAccount)
router.post('/auth/accounts/disconnect', authMiddleware, disconnectExternalAccount)
router.get('/auth/accounts/oauth/callback', authMiddleware, handleOAuthCallback)
router.get('/auth/accounts', authMiddleware, GetUserExternalAccounts)
router.post('/auth/accounts/activate', authMiddleware, toggleExternalAccountActivation)



router.get('/calendar/sync', authMiddleware,syncSchedulesToExternalCalendars)
router.get('/check-stripe', authMiddleware,checkStripeConnected)
export default router;