{"name": "puzzle-piece-api", "version": "1.0.0", "description": "", "main": "index.ts", "scripts": {"build": "npx tsc", "dev": "nodemon --exec 'ts-node' index.ts", "start": "ts-node index.ts", "test": "echo \"Error: no test specified\" && exit 1"}, "author": "Puzzle Piece Solutions", "license": "ISC", "dependencies": {"@azure/msal-node": "^2.15.0", "axios": "^1.7.7", "bcrypt": "^5.1.1", "body-parser": "^2.2.0", "cloudinary": "^1.41.3", "cors": "^2.8.5", "crypto-js": "^4.2.0", "dotenv": "^16.4.5", "express": "^4.19.2", "express-rate-limit": "^7.5.0", "express-session": "^1.18.1", "googleapis": "^144.0.0", "handlebars": "^4.7.8", "jsonwebtoken": "^9.0.2", "moment-timezone": "^0.5.46", "mongoose": "^8.7.3", "multer": "^2.0.0", "multer-storage-cloudinary": "^4.0.0", "nodemailer": "^6.9.13", "socket.io": "^4.7.5", "stripe": "^17.2.1", "ts-node": "^10.9.2", "uuid": "^11.1.0"}, "devDependencies": {"@types/bcrypt": "^5.0.2", "@types/cors": "^2.8.17", "@types/crypto-js": "^4.2.2", "@types/express": "^4.17.21", "@types/express-session": "^1.18.0", "@types/jsonwebtoken": "^9.0.7", "@types/multer": "^1.4.12", "@types/node": "^22.6.1", "@types/nodemailer": "^6.4.16", "nodemon": "^3.1.7", "ts-node": "^10.9.2", "tsx": "^4.19.2", "typescript": "^5.6.2"}}