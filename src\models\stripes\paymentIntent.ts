import mongoose, { Document, Schema } from 'mongoose';

interface IPaymentIntent extends Document {
  intentType: 'credit' | 'service'
  intentTypeId: string;
  email: string;
  userId?: mongoose.Types.ObjectId;
  stripePaymentIntentId: string;
  status: string;
  clientSecret:string;
}

const PaymentIntentSchema: Schema = new Schema<IPaymentIntent>({
  intentType: {
    type: String,
    enum: ['credit', 'service'],
    required: true,
  },
  intentTypeId: {
    type: String,
    required: true,
  },
  email: {
    type: String,
    required: true,
  },
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: function (this: IPaymentIntent) {
      return this.intentType === 'credit';
    },
  },
  stripePaymentIntentId: {
    type: String,
    required: true,
  },
  clientSecret:{
    type: String,
    required: true,
  },
  status: {
    type: String,
    required: true,
  },
}, { timestamps: true });

// Create the model
export default mongoose.model<IPaymentIntent>('PaymentIntent', PaymentIntentSchema);

