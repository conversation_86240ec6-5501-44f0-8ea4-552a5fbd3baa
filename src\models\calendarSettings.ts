import mongoose, { Document, Schema } from 'mongoose';

interface IWorkday {
  day: 'sunday' | 'monday' | 'tuesday' | 'wednesday' | 'thursday' | 'friday' | 'saturday';
  startTime: string;  // HH:MM format
  closingTime: string;  // HH:MM format
}

export interface ICalendarSettings extends Document {
  workdays: IWorkday[]; // Each workday with its own start and closing time
  timeslots: '10' | '15' | '30' | '60' | 'custom';
  customTimeslotDuration?: number;
  userId: mongoose.Types.ObjectId;
  timezone: string;
  advanceBookingLimit?: number;
}
const workdaySchema = new Schema({
    day: {
      type: String,
      enum: ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'],
      required: true,
    },
    startTime: {
      type: String,
      required: true,
      validate: {
        validator: function(v: string) {
          return /^([01]\d|2[0-3]):([0-5]\d)$/.test(v); // Matches HH:MM format
        },
        message: 'Invalid time format. Expected HH:MM.'
      }
    },
    closingTime: {
      type: String,
      required: true,
      validate: {
        validator: function(v: string) {
          return /^([01]\d|2[0-3]):([0-5]\d)$/.test(v); // Matches HH:MM format
        },
        message: 'Invalid time format. Expected HH:MM.'
      }
    }
  });
  
  const calendarSettingsSchema: Schema<ICalendarSettings> = new Schema({
    workdays: {
      type: [workdaySchema],
      default: [
        { day: 'monday', startTime: '09:00', closingTime: '17:00' },
        { day: 'tuesday', startTime: '09:00', closingTime: '17:00' },
        { day: 'wednesday', startTime: '09:00', closingTime: '17:00' },
        { day: 'thursday', startTime: '09:00', closingTime: '17:00' },
        { day: 'friday', startTime: '09:00', closingTime: '17:00' }
      ]
    },
    timeslots: {
      type: String,
      enum: ['10', '15', '30', '60', 'custom'], 
      required: true,
      default: 'custom'
    },
    customTimeslotDuration: {
      type: Number,
      default:30,
      required: function (this: ICalendarSettings) {
        return this.timeslots === 'custom';
      }
    },
    userId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true,
      unique:true
    },
    timezone: {
      type: String,
      required: true,
      default: 'Europe/London'
    },
    advanceBookingLimit: {
        type: Number,
        default:60,
        validate: {
          validator: function(v: number) {
            return v > 0; 
          },
          message: 'Advance booking limit must be a positive number.'
        }
      }
  }, { timestamps: true });
  
  export default mongoose.model<ICalendarSettings>('CalendarSettings', calendarSettingsSchema);
  