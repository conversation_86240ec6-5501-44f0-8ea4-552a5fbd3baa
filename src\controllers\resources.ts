import {Response, NextFunction } from 'express';
import userFeedbacks from '../models/userFeedbacks';
import { sendEmail } from '../services/emailService';
import { BlockedIP } from '../models/resources';

export const sendUserFeedback = async (req: any, res: Response, next: NextFunction):Promise<Response> => {
    try {
        const { fullName, email, feedback, type } = req.body;
        const newFeedback = new userFeedbacks({
            fullName, email, feedback, type
        })
        await newFeedback.save()
        if(!process.env.SUPPORT_EMAIL){
          throw new Error("Add an support email in the environment variable")
        }

        await sendEmail(
            process.env.SUPPORT_EMAIL,
            'User Feedback',
            'support.hbs',{
              fullName,
              email,
              feedback,
              type,
            }
        )
        return res.status(200).json({ message: 'Feedback submitted successfully!' });
    } catch (error:any) {
        next(error);
        console.error('Error sending user feedback:', error);
        return res.status(500).json({ error: 'Error sending user feedback', message:error?.message});
    }
}

export const blockIp = async (req: any, res: Response, next: NextFunction):Promise<Response> => {
    const { ip } = req.body;
  
    if (!ip) {
      return res.status(400).json({ error: "IP address is required" });
    }
    try {
      await BlockedIP.create({ ip });
      return res.status(201).json({ message: `IP ${ip} blocked successfully.` });
    } catch (error) {
      console.error("Error blocking IP:", error);
      return res.status(500).json({ error: "Failed to block IP" });
    }
}
export const unBlockIp = async (req: any, res: Response, next: NextFunction):Promise<Response> => {
    const { ip } = req.body;
  
    if (!ip) {
      return res.status(400).json({ error: "IP address is required" });
    }
  
    try {
      await BlockedIP.deleteOne({ ip });
      return res.status(200).json({ message: `IP ${ip} unblocked successfully.` });
    } catch (error) {
      console.error("Error unblocking IP:", error);
      return res.status(500).json({ error: "Failed to unblock IP" });
    }
}
