# Puzzle Piece API  Documentation
---
# API Documentation

## Table of Contents
1. [Overview](#overview)
2. [Getting Started](#getting-started)
   - [Base URL](#base-url)
   - [Authentication](#authentication)
3.  [Endpoints](#endpoints)
   - [Authentication](#authentication)
   -[Bookings](#bookings)
   - [Subscriptions](#subscriptions)
   - [Analytics](#analytics)
4. [Admin Endpoints](#admin-endpoints)
   - [Users](#users)
   - [UserLogs](#userlogs)
   - [FAQ Management](#faq-management)
   - [Blog Management](#blog-management)
   - [Tag Management](#tag-management)
   - [Support Tickets](#support-tickets)
   - [Promo Codes Management](#promo-code-management)
5. [Error Handling](#error-handling)
6. [Contact](#contact)

---

## Overview
Welcome to the **[Puzzle Piece] API**! This API allows developers to access and interact with the [Puzzle Piece] platform.

### Features:
- [Feature 1]
- [Feature 2]
- [Feature 3]

---

## Getting Started

### Base URL
The base URL for all API requests is:

```plaintext
https://api.example.com/api
```
## Endpoints
### Authentication
This API uses **[authentication type,Bearer Token]** for secure access. 

- **Authentication Header:**
  ```plaintext
  Authorization: Bearer <your_token_here>
  ```
---

### Bookings
#### 1. Update a Booking Status
**Endpoint:** `PUT /bookings/:bookingId/status`
**Description:** Updates the status of a booking.
**Request:**
```http
PUT /bookings/:bookingId/status HTTP/1.1
Host: api.example.com
Authorization: Bearer <your_token_here>

{
  "status": "cancelled"
}
```
**Response:**
- **200 OK**
```json
{ success:true,
  "booking": [
    
  ]
}
```
#### 2. Reschedule a Booking
**Endpoint:** `PUT /bookings/:bookingId/reschedule`
**Description:** Reschedules a booking.
**Request:**
**Request:**
```http
PUT /bookings/:bookingId/reschedule HTTP/1.1
Host: api.example.com
Authorization: Bearer <your_token_here>

{
  "newDate": "",
  "timeSlot": ""
}
```
**Response:**
- **200 OK**
```json
{ success:true,
  "booking": [
    
  ]
}
```


### Subscriptions
#### 1. Create a Subscription
**Endpoint:** `PUT /upgrade-subscription`
**Description:** Upgrades a user subscription.
**Request:**
```http
PUT /upgrade-subscription HTTP/1.1
Host: api.example.com
Authorization: Bearer <your_token_here>
Content-Type: application/json

{
  "planId": "fgerrgt432342212"
}
```
**Response:**
- **200 OK**
```json
{   "success":true,
    message: 'Subscription upgraded successfully',
    subscription: {},
    clientSecret: ""
}
```

### Analytics
#### 1. Get Monthly Service Return Rate
**Endpoint:** `GET /monthly-return-rate/service/:serviceId`  
**Description:** Fetches the monthly service return rate for a specific service.
**Request:**
```http
GET /monthly-return-rate/service/:serviceId HTTP/1.1
Host: api.example.com
Authorization: Bearer <your_token_here>
```
**Response:**
- **200 OK**

#### 2. Get Quarterly Service Return Rate
**Endpoint:** `GET /quaterly-return-rate/service/:serviceId`
**Description:** Fetches the quaterly service return rate for a specific service.
**Request:**
```http
GET /quaterly-return-rate/service/:serviceId HTTP/1.1
Host: api.example.com
Authorization: Bearer <your_token_here>
```
**Response:**
- **200 OK**

#### 3. Get Services Average Revenue
**Endpoint:** `GET /appointment/average-revenue-service`
**Description:** Fetches the Average Revenue for every services a user has and also the the service with the highest revenue.
**Request:**
```http
GET /appointment/average-revenue-service
Host: api.example.com
Authorization: Bearer <your_token_here>
```
**Response:**
- **200 OK**

#### 4. Get Appointment Return Rate
**Endpoint:** `GET /appointment/return-rate/:serviceId?timeInterval`
**Description:** Fetches the Return Rate for a Business Service during a time interval
**Request:**
```http
GET /appointment/return-rate/:serviceId?timeInterval=''
Params: timeInterval: 'today'|'Monthly' | 'weekly' | 'yearly'
Host: api.example.com
Authorization: Bearer <your_token_here>
```
**Response:**
- **200 OK**
-
```
serviceData:{
    "serviceId": "service1_id",
    "serviceName": "Service 1",
    "returnRates": [
      {
        "timeFrame": "Time Period Label",
        "totalClients": 10,
        "totalReturningClients": 4,
        "returnRate": "40.00%"
      },
      // More time frames...
    ]
  },
```


#### 5. Get Most Used Service
**Endpoint:** `GET /appointment/most-used-services?timeInterval=''`
**Description:** Fetches data for the most used Service for a Business
**Request:**
```http
GET /appointment/most-used-services?timeInterval=''
Params: timeInterval: 'today'|'Monthly' | 'weekly' | 'yearly'
Host: api.example.com
Authorization: Bearer <your_token_here>
```
**Response:**
- **200 OK**
-
```
mostUsedServices:[
  timeFrame: 'Time Period Label',
  service:{}
  count: 23,
],
```
#### 6. Get Client Count and Percentage
**Endpoint:** `GET /appointment/client-percentage/:serviceId?timeInterval=''`
**Description:** Fetches the client count and percentage of a specific for a Business
**Request:**
```http
GET /appointment/client-percentage/:serviceId?timeInterval=''
Params: timeInterval: 'today'|'Monthly' | 'weekly' | 'yearly'
Host: api.example.com
Authorization: Bearer <your_token_here>
```
**Response:**
- **200 OK**
-
```
{
  "message": "Client count and percentage for the service retrieved successfully.",
  "serviceName": "Haircut",
  "serviceId": "service1_id",
  "data": [
    {
      "timeFrame": "Today",
      "serviceName": "Haircut",
      "clientCount": 5,
      "percentage": "10.00%"
    }
  ]
}

```





#### 7. Get Average Revenue For A Service
**Endpoint:** `GET /appointment/average-revenue-service:serviceId?timeInterval=''`
**Description:** Fetches the Average Revenue and Overall Average of a specific Service for a Business
**Request:**
```http
GET /appointment/average-revenue-service:serviceId?timeInterval=''
Params: timeInterval: 'today'|'Monthly' | 'weekly' | 'yearly'
Host: api.example.com
Authorization: Bearer <your_token_here>
```
**Response:**
- **200 OK**
-
```
{
  "message": "Average revenue data retrieved successfully.",
  "serviceId": "service1_id",
  "serviceName": "Haircut",
  "overallAverageRevenue": 45.75,
  "data": [
    {
      "timeFrame": "Today",
      "serviceName": "Haircut",
      "totalRevenue": 250,
      "appointmentCount": 5,
      "averageRevenue": 50
    }
  ]
  ....
}


```






#### 8. Get Service with Highest Avergae Revenue
**Endpoint:** `GET /appointment/highest-average-revenue-service:serviceId?timeInterval=''`
**Description:** Fetches the Service with the Highest Average Revenue for a Business
**Request:**
```http
GET /appointment/highest-average-revenue-service:serviceId?timeInterval=''
Params: timeInterval: 'today'|'Monthly' | 'weekly' | 'yearly'
Host: api.example.com
Authorization: Bearer <your_token_here>
```
**Response:**
- **200 OK**
-
```
{
  "message": "Service with highest average revenue retrieved successfully.",
  "service": {
    "id": "service3_id",
    "name": "Premium Facial",
    "totalRevenue": 1500,
    "appointmentCount": 15,
    "averageRevenue": 100
  },
  "timeFrameData": [
    {
      "timeFrame": "Monday",
      "totalRevenue": 200,
      "appointmentCount": 2,
      "averageRevenue": 100
    },
    {
      "timeFrame": "Tuesday",
      "totalRevenue": 300,
      "appointmentCount": 3,
      "averageRevenue": 100
    },
    // Other days of the week...
  ]
}


```


#### 9. Get Service with Highest Total Revenue
**Endpoint:** `GET /appointment/highest-total-revenue-service:serviceId?timeInterval=''`
**Description:** Fetches the Service with the Highest Revenue for a Business
**Request:**
```http
GET /appointment/highest-total-revenue-service:serviceId?timeInterval=''
Params: timeInterval: 'today'|'Monthly' | 'weekly' | 'yearly'
Host: api.example.com
Authorization: Bearer <your_token_here>
```
**Response:**
- **200 OK**
-
```
{
  "message": "Service with highest total revenue retrieved successfully.",
  "service": {
    "id": "service1_id",
    "name": "Haircut",
    "totalRevenue": 2500,
    "appointmentCount": 50,
    "averageRevenue": 50
  },
  "timeFrameData": [
    {
      "timeFrame": "Monday",
      "totalRevenue": 350,
      "appointmentCount": 7,
      "averageRevenue": 50
    },
    {
      "timeFrame": "Tuesday",
      "totalRevenue": 400,
      "appointmentCount": 8,
      "averageRevenue": 50
    },
    // Other days of the week...
  ]
}



```



## Admin Endpoints

### Users

#### 1. Get All Users
**Endpoint:** `GET /admin/user`  
**Description:** Fetches a list of all users.

**Request:**
```http
GET admin/users HTTP/1.1
Host: api.example.com
Authorization: Bearer <your_token_here>
```

**Response:**
- **200 OK**
```json
{
  "data": [
    {
      "_id": 1,
      "first_name": "John",
      "last_name": "Doe",
      "email": "<EMAIL>"
    },
    {
      "_id": 2,
      "first_name": "Jane",
      "last_name": "Doe",
      "email": "<EMAIL>"
    }
  ]
}
```

#### 2. Create a New User
**Endpoint:** `POST /admin/user`  
**Description:** Creates a new user.

**Request:**
```http
POST admin/users HTTP/1.1
Host: api.example.com
Authorization: Bearer <your_token_here>
Content-Type: application/json

{
  "first_name": "John",
  "last_name": "Doe",
  "username": "johndoe123",
  "email": "<EMAIL>",
  "password": "securePassword123!"
}
```

**Response:**
- **201 Created**
```json
{
  "_id": 1,
  "first_name": "John",
  "last_name": "Doe",
  "email": "<EMAIL>"
}
```

---

#### 3. Delete a User
**Endpoint:** `POST /admin/user/:userId`  
**Description:** Creates a new user.

**Request:**
```http
DELETE /admin/users HTTP/1.1
Host: api.example.com
Authorization: Bearer <your_token_here>
Content-Type: application/json
```

**Response:**
- **200 Created**
```json
{   "success":true,
    "message": 'User deleted successfully' 
}
```

---

#### 4. Toggle User Activation Status
**Endpoint:** `PATCH /admin/toggle-status/:userId`  
**Description:** updates the user status.

**Request:**
```http
PATCH /admin/toggle-status/:userId HTTP/1.1
Host: api.example.com
Authorization: Bearer <your_token_here>
Content-Type: application/json

{
  "userId": "fgerrgt432342212",
  "isActive": "true",
}
```

**Response:**
- **200 Created**
```json
{   "success":true,
    "message": 'User account activated successfully' 
}
```

---








### UsersLogs

#### 1. Get All Users with Logs
**Endpoint:** `GET /admin/logs/users`  
**Description:** Fetches a list of all users with lOGS.

**Request:**
```http
GET /admin/logs/users HTTP/1.1
Host: api.example.com
Authorization: Bearer <your_token_here>
```

**Response:**
- **200 OK**
```json
{
  "data": [
    {
      "_id": 1,
      "first_name": "John",
      "last_name": "Doe",
      "email": "<EMAIL>"
    },
    {
      "_id": 2,
      "first_name": "Jane",
      "last_name": "Doe",
      "email": "<EMAIL>"
    }
  ]
}
```

#### 2. Get a user logs
**Endpoint:** `GET /admin/logs/:userId`  
**Description:** Fetches a particular user logs.

**Request:**
```http
GET /admin/logs/:userId HTTP/1.1
Host: api.example.com
Authorization: Bearer <your_token_here>
```

**Response:**
- **200 OK**
```json
{
  "data": [
  ]
}
```

### FAQ Management

#### 1. Get All FAQs  
**Endpoint:** `GET /admin/faq`  
**Description:** Fetches a list of all FAQs.  

**Request:**  
```http
GET /admin/faq HTTP/1.1
Host: api.example.com
Authorization: Bearer <your_token_here>
```

**Response:**  
- **200 OK**  
```json
{
  "data": [
    {
      "_id": "1",
      "question": "What is the API?",
      "answer": "The API provides functionality to manage data and access features.",
      "category": "General"
    },
    {
      "_id": "2",
      "question": "How do I authenticate?",
      "answer": "Use the Bearer token provided after logging in.",
      "category": "Authentication"
    }
  ]
}
```

---

#### 2. Get FAQ by ID  
**Endpoint:** `GET /admin/faq/:Id`  
**Description:** Fetches a specific FAQ by its ID.  

**Request:**  
```http
GET /admin/faq/:Id HTTP/1.1
Host: api.example.com
Authorization: Bearer <your_token_here>
```

**Response:**  
- **200 OK**  
```json
{
  "_id": "1",
  "question": "What is the API?",
  "answer": "The API provides functionality to manage data and access features.",
  "category":"General"
}
```

- **404 Not Found**  
```json
{
  "error": "FAQ not found"
}
```

---

#### 3. Create a New FAQ  
**Endpoint:** `POST /admin/faq`  
**Description:** Creates a new FAQ.  

**Request:**  
```http
POST /admin/faq HTTP/1.1
Host: api.example.com
Authorization: Bearer <your_token_here>
Content-Type: application/json

{
  "question": "How do I reset my password?",
  "answer": "Click on 'Forgot Password' on the login page.",
  "category": "Authentication"
}
```

**Response:**  
- **201 Created**  
```json
{
  "_id": "3",
  "question": "How do I reset my password?",
  "answer": "Click on 'Forgot Password' on the login page.",
  "caregory": "Authentication"
}
```

- **400 Bad Request**  
```json
{
  "error": "Invalid input"
}
```

---

#### 4. Update an FAQ  
**Endpoint:** `PUT /admin/faq/:Id`  
**Description:** Updates an existing FAQ by ID.  

**Request:**  
```http
PUT /admin/faq/:Id HTTP/1.1
Host: api.example.com
Authorization: Bearer <your_token_here>
Content-Type: application/json

{
  "question": "What is the updated API?",
  "answer": "The API now supports additional features.",
  "category": "General"
}
```

**Response:**  
- **200 OK**  
```json
{
  "_id": "1",
  "question": "What is the updated API?",
  "answer": "The API now supports additional features.",
  "category": "General"
}
```

- **404 Not Found**  
```json
{
  "error": "FAQ not found"
}
```

---

#### 5. Delete an FAQ  
**Endpoint:** `DELETE /admin/faq/:Id`  
**Description:** Deletes an FAQ by ID.  

**Request:**  
```http
DELETE /admin/faq/:Id HTTP/1.1
Host: api.example.com
Authorization: Bearer <your_token_here>
```

**Response:**  
- **200 OK**  
```json
{
  "success": true,
  "message": "FAQ deleted successfully"
}
```

- **404 Not Found**  
```json
{
  "error": "FAQ not found"
}
```

### Blog Management  

#### 1. Create a New Blog  
**Endpoint:** `POST /admin/blogs`  
**Description:** Creates a new blog post.  

**Request:**  
```http
POST /admin/blogs HTTP/1.1  
Host: api.example.com  
Authorization: Bearer <your_token_here>  
Content-Type: multipart/form-data  

{
  "title": "The Future of AI",
  "shortDescription": "An overview of AI advancements.",
  "longDescription": "Artificial Intelligence (AI) has been transforming industries...",
  "authorName": "Jane Doe",
  "tags": ["AI", "Technology"],
  "timePosted": "2024-10-21T12:30:00Z",
  "image": <image_file>
}
```

**Response:**  
- **201 Created**  
```json
{
  "success": true,
  "data": {
    "_id": "1",
    "title": "The Future of AI",
    "shortDescription": "An overview of AI advancements.",
    "longDescription": "Artificial Intelligence (AI) has been transforming industries...",
    "authorName": "Jane Doe",
    "tags": ["AI", "Technology"],
    "timePosted": "2024-10-21T12:30:00Z",
    "image": "https://example.com/uploads/blog_image.jpg"
  }
}
```

---

#### 2. Update a Blog  
**Endpoint:** `PUT /admin/blogs/:Id`  
**Description:** Updates an existing blog post by ID.  

**Request:**  
```http
PUT /admin/blogs/:Id HTTP/1.1  
Host: api.example.com  
Authorization: Bearer <your_token_here>  
Content-Type: multipart/form-data  

{
  "title": "The Future of AI - Updated",
  "shortDescription": "A comprehensive overview of AI advancements.",
  "longDescription": "Artificial Intelligence (AI) continues to revolutionize...",
  "authorName": "Jane Doe",
  "tags": ["AI", "Technology", "Innovation"],
  "timePosted": "2024-10-21T15:00:00Z",
  "image": <updated_image_file>
}
```

**Response:**  
- **200 OK**  
```json
{
  "success": true,
  "message": "Blog updated successfully"
}
```

---

#### 3. Get a Blog by ID  
**Endpoint:** `GET /admin/blogs/:Id`  
**Description:** Retrieves details of a blog post by its ID.  

**Request:**  
```http
GET /admin/blogs/:Id HTTP/1.1  
Host: api.example.com  
Authorization: Bearer <your_token_here>
```

**Response:**  
- **200 OK**  
```json
{
  "_id": "1",
  "title": "The Future of AI",
  "shortDescription": "An overview of AI advancements.",
  "longDescription": "Artificial Intelligence (AI) has been transforming industries...",
  "authorName": "Jane Doe",
  "tags": ["AI", "Technology"],
  "timePosted": "2024-10-21T12:30:00Z",
  "image": "https://example.com/uploads/blog_image.jpg"
}
```

---

#### 4. Get All Blogs  
**Endpoint:** `GET /admin/blogs`  
**Description:** Fetches a list of all blogs.  

**Request:**  
```http
GET /admin/blogs HTTP/1.1  
Host: api.example.com  
Authorization: Bearer <your_token_here>
```

**Response:**  
- **200 OK**  
```json
{
  "data": [
    {
      "_id": "1",
      "title": "The Future of AI",
      "shortDescription": "An overview of AI advancements.",
      "authorName": "Jane Doe",
      "tags": ["AI", "Technology"],
      "timePosted": "2024-10-21T12:30:00Z",
      "image": "https://example.com/uploads/blog_image.jpg"
    },
    {
      "_id": "2",
      "title": "Understanding Blockchain",
      "shortDescription": "Basics of blockchain technology.",
      "authorName": "John Smith",
      "tags": ["Blockchain", "Technology"],
      "timePosted": "2024-10-22T10:00:00Z",
      "image": "https://example.com/uploads/blog_image2.jpg"
    }
  ]
}
```

---

#### 5. Delete a Blog  
**Endpoint:** `DELETE /admin/blogs/:Id`  
**Description:** Deletes a blog post by its ID.  

**Request:**  
```http
DELETE /admin/blogs/:Id HTTP/1.1  
Host: api.example.com  
Authorization: Bearer <your_token_here>
```

**Response:**  
- **200 OK**  
```json
{
  "success": true,
  "message": "Blog deleted successfully"
}
```

---

### Tag Management  

#### 1. Create a New Tag  
**Endpoint:** `POST /admin/tags`  
**Description:** Creates a new tag for blogs.  

**Request:**  
```http
POST /admin/tags HTTP/1.1  
Host: api.example.com  
Authorization: Bearer <your_token_here>  
Content-Type: application/json  

{
  "name": "Technology"
}
```

**Response:**  
- **201 Created**  
```json
{
  "success": true,
  "data": {
    "_id": "1",
    "name": "Technology"
  }
}
```

---

#### 2. Get All Tags  
**Endpoint:** `GET /admin/tags`  
**Description:** Fetches all available tags.  

**Request:**  
```http
GET /admin/tags HTTP/1.1  
Host: api.example.com  
Authorization: Bearer <your_token_here>
```

**Response:**  
- **200 OK**  
```json
{
  "data": [
    {
      "_id": "1",
      "name": "Technology"
    },
    {
      "_id": "2",
      "name": "AI"
    }
  ]
}
```

---

#### 3. Delete a Tag  
**Endpoint:** `DELETE /admin/tag/:Id`  
**Description:** Deletes a tag by its ID.  

**Request:**  
```http
DELETE /admin/tag/:Id HTTP/1.1  
Host: api.example.com  
Authorization: Bearer <your_token_here>
```

**Response:**  
- **200 OK**  
```json
{
  "success": true,
  "message": "Tag deleted successfully"
}
```
---

### Support Tickets

#### 1. Create a New Support Ticket  
**Endpoint:** `POST /support-tickets`  
**Description:** Allows a user to raise a new support ticket.  

**Request:**  
```http
POST /support-tickets HTTP/1.1  
Host: api.example.com  
Authorization: Bearer <your_token_here>  
Content-Type: application/json  

{
  "userId": "63e8c99f6c441c1ecf0aeb2d",
  "title": "Issue with Payment",
  "description": "I am unable to process my payment for the order.",
  "priority": "high"
}
```

**Response:**  
- **201 Created**  
```json
{
  "message": "Support ticket created successfully",
  "ticket": {
    "_id": "64b6c8f2c5e5c1a1e9bcd019",
    "userId": "63e8c99f6c441c1ecf0aeb2d",
    "title": "Issue with Payment",
    "description": "I am unable to process my payment for the order.",
    "status": "open",
    "priority": "high",
    "ip": "***********",
    "createdAt": "2024-11-03T12:00:00.000Z",
    "updatedAt": "2024-11-03T12:00:00.000Z"
  }
}
```

---

#### 2. View All Support Tickets  
**Endpoint:** `GET /admin/tickets`  
**Description:** Fetches a list of all support tickets for an admin to manage.  

**Request:**  
```http
GET /admin/tickets/list HTTP/1.1  
Host: api.example.com  
Authorization: Bearer <admin_token_here>
```

**Response:**  
- **200 OK**  
```json
[
  {
    "_id": "64b6c8f2c5e5c1a1e9bcd019",
    "userId": {
      "_id": "63e8c99f6c441c1ecf0aeb2d",
      "username": "johndoe",
      "email": "<EMAIL>"
    },
    "title": "Issue with Payment",
    "description": "I am unable to process my payment for the order.",
    "status": "open",
    "priority": "high",
    "ip": "***********",
    "createdAt": "2024-11-03T12:00:00.000Z",
    "updatedAt": "2024-11-03T12:00:00.000Z"
  },
  {
    "_id": "64b6c8f2c5e5c1a1e9bcd020",
    "userId": {
      "_id": "63e8c99f6c441c1ecf0aeb2e",
      "username": "janedoe",
      "email": "<EMAIL>"
    },
    "title": "Unable to Reset Password",
    "description": "I did not receive the password reset link.",
    "status": "resolved",
    "priority": "medium",
    "ip": "***********",
    "createdAt": "2024-11-01T10:00:00.000Z",
    "updatedAt": "2024-11-02T08:00:00.000Z"
  }
]
```

---

#### 3. Update Ticket Status  
**Endpoint:** `PATCH /admin/tickets/update`  
**Description:** Allows an admin to update the status of a support ticket.  

**Request:**  
```http
PATCH /tickets/update HTTP/1.1  
Host: api.example.com  
Authorization: Bearer <admin_token_here>  
Content-Type: application/json  

{
  "ticketId": "64b6c8f2c5e5c1a1e9bcd019",
  "status": "in-progress"
}
```

**Response:**  
- **200 OK**  
```json
{
  "message": "Ticket status updated successfully",
  "ticket": {
    "_id": "64b6c8f2c5e5c1a1e9bcd019",
    "userId": "63e8c99f6c441c1ecf0aeb2d",
    "title": "Issue with Payment",
    "description": "I am unable to process my payment for the order.",
    "status": "in-progress",
    "priority": "high",
    "ip": "***********",
    "createdAt": "2024-11-03T12:00:00.000Z",
    "updatedAt": "2024-11-03T13:00:00.000Z"
  }
}
```

---

#### 4. Delete a Support Ticket  
**Endpoint:** `DELETE /admin/tickets/:ticketId`  
**Description:** Allows an admin to delete a support ticket.  

**Request:**  
```http
DELETE /tickets/64b6c8f2c5e5c1a1e9bcd019 HTTP/1.1  
Host: api.example.com  
Authorization: Bearer <admin_token_here>
```

**Response:**  
- **200 OK**  
```json
{
  "message": "Ticket deleted successfully"
}
```

---

### Promo Code Management  

#### 1. Create a Promo Code  
**Endpoint:** `POST /admin/promo-code/`  
**Description:** Creates a new promo code.  

**Request:**  
```http
POST /admin/promo-code/ HTTP/1.1  
Host: api.example.com  
Authorization: Bearer <your_token_here>  
Content-Type: application/json  

{
  "code": "SAVE10",
  "type": "subscription",
  "discount": {
    "type": "percentage",
    "amount": 10
  },
  "description": "Get 10% off on your subscription",
  "expiringDate": "2024-12-31T23:59:59Z",
  "isActive": true,
  "maxUsage": 100
}
```

**Response:**  
- **201 Created**  
```json
{
  "success": true,
  "data": {
    "_id": "1",
    "code": "SAVE10",
    "type": "subscription",
    "discount": {
      "type": "percentage",
      "amount": 10
    },
    "description": "Get 10% off on your subscription",
    "expiringDate": "2024-12-31T23:59:59Z",
    "isActive": true,
    "maxUsage": 100
  }
}
```

---

#### 2. Get All Promo Codes  
**Endpoint:** `GET /admin/promo-code/`  
**Description:** Fetches a list of all promo codes.  

**Request:**  
```http
GET /admin/promo-code/ HTTP/1.1  
Host: api.example.com  
Authorization: Bearer <your_token_here>
```

**Response:**  
- **200 OK**  
```json
{
  "data": [
    {
      "_id": "1",
      "code": "SAVE10",
      "type": "subscription",
      "discount": {
        "type": "percentage",
        "amount": 10
      },
      "description": "Get 10% off on your subscription",
      "expiringDate": "2024-12-31T23:59:59Z",
      "isActive": true,
      "maxUsage": 100
    },
    {
      "_id": "2",
      "code": "BUY50",
      "type": "credit_purchase",
      "discount": {
        "type": "fixed",
        "amount": 50
      },
      "description": "Get $50 off your next credit purchase",
      "expiringDate": "2024-11-30T23:59:59Z",
      "isActive": false,
      "maxUsage": 50
    }
  ]
}
```

---

#### 3. Get Promo Code by ID  
**Endpoint:** `GET /admin/promo-code/:Id`  
**Description:** Retrieves details of a promo code by its ID.  

**Request:**  
```http
GET /admin/promo-code/:Id HTTP/1.1  
Host: api.example.com  
Authorization: Bearer <your_token_here>
```

**Response:**  
- **200 OK**  
```json
{
  "_id": "1",
  "code": "SAVE10",
  "type": "subscription",
  "discount": {
    "type": "percentage",
    "amount": 10
  },
  "description": "Get 10% off on your subscription",
  "expiringDate": "2024-12-31T23:59:59Z",
  "isActive": true,
  "maxUsage": 100
}
```

---

#### 4. Update a Promo Code  
**Endpoint:** `PUT /admin/promo-code/:Id`  
**Description:** Updates an existing promo code by ID.  

**Request:**  
```http
PUT /admin/promo-code/:Id HTTP/1.1  
Host: api.example.com  
Authorization: Bearer <your_token_here>  
Content-Type: application/json  

{
  "code": "SAVE15",
  "type": "subscription",
  "discount": {
    "type": "percentage",
    "amount": 15
  },
  "description": "Get 15% off on your subscription",
  "expiringDate": "2024-12-31T23:59:59Z",
  "isActive": true,
  "maxUsage": 150
}
```

**Response:**  
- **200 OK**  
```json
{
  "success": true,
  "message": "Promo code updated successfully"
}
```

---

#### 5. Delete a Promo Code  
**Endpoint:** `DELETE /admin/promo-code/:Id`  
**Description:** Deletes a promo code by its ID.  

**Request:**  
```http
DELETE /admin/promo-code/:Id HTTP/1.1  
Host: api.example.com  
Authorization: Bearer <your_token_here>
```

**Response:**  
- **200 OK**  
```json
{
  "success": true,
  "message": "Promo code deleted successfully"
}
```

## Error Handling

All error responses follow the format below:

```json
{
  success: false,
  error:"Error message"
}
```

### Common Errors:
- **400 Bad Request:** Invalid input.
- **401 Unauthorized:** Authentication failed.
- **403 Forbidden:** Insufficient permissions.
- **404 Not Found:** Resource not found.
- **500 Internal Server Error:** Server-side error.

---

<!-- ## Rate Limiting
The API is rate-limited to prevent abuse. The limits are as follows:

| Plan         | Requests Per Minute |
|--------------|---------------------|
| Free         | 60                 |
| Pro          | 120                |

If the limit is exceeded, the following response will be returned:

**429 Too Many Requests**
```json
{
  "error": {
    "code": "rate_limit_exceeded",
    "message": "You have exceeded the rate limit. Please try again later."
  }
}
``` -->

---

## Contact
If you have any questions or need support, feel free to contact us:

- **Email:** <EMAIL>  
- **Website:** [https://example.com](https://example.com)  
- **Documentation Portal:** [https://docs.example.com](https://docs.example.com)  

---

## License
This API is licensed under the **[MIT License/Your Preferred License]**. See the [LICENSE](LICENSE) file for more details.