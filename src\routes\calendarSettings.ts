import { Router } from 'express';
import { authMiddleware } from '../middleware/authService';
import { createBlockedTime, deleteBlockedTime, getAvailableTimeSlots, getAvailableWorkingDays, getBlockedTimesByCalendar, getBlockedTimesByUser, getCalendarSettings, updateBlockedTime, updateCalendarSettings } from '../controllers/calendarSettings';
const router: Router = Router();



router.get('/calender-settings/',authMiddleware,getCalendarSettings);
router.put('/update/calender-settings/',authMiddleware,updateCalendarSettings);
// router.get('/available-times/:userId', getAvailableTimeslots);

router.post('/time-block/:calendarId',authMiddleware,createBlockedTime);
router.get('/time-block',authMiddleware,getBlockedTimesByUser);
router.get('/time-block/:calendarId',authMiddleware,getBlockedTimesByCalendar);
router.put('/time-block/:blockedTimeId',authMiddleware,updateBlockedTime);
router.delete('/time-block/:blockedTimeId',authMiddleware,deleteBlockedTime);


router.get('/get-workdays/:userId',getAvailableWorkingDays);
router.post('/available-times/:userId',getAvailableTimeSlots); 

export default router;