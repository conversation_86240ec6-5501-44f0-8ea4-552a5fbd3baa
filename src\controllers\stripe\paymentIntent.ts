import {Response, NextFunction } from "express";
import paymentIntent from "../../models/stripes/paymentIntent";

export const checkExistingPaymentIntent = async (req: any, res: Response, next: NextFunction): Promise<Response> => {
    const {intentType, clientEmail,userId }= req.body
    try {
        let paymentIntentData
        if (intentType==='service'){
            paymentIntentData = await paymentIntent.findOne({
                email:clientEmail,
                intentType
            })
            return res.status(200).json({
                success:true,
                paymentIntentData
            })
        }else if (intentType ==='credit'){
            const paymentIntentData = await paymentIntent.findOne({
                userId,
                intentType
            })
            return res.status(200).json({
                success:true,
                paymentIntentData
            })
        }
        else{
            return res.status(400).json({
                success:false,
                error:'Invalid IntentType'
            })
        }

    } catch (error) {
        next(error)
        console.log("service payment error", error)
        return res.status(500).json({ error: 'Error using credits' });
    }
}
