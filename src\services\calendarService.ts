import axios from "axios";
import dotenv from "dotenv";
import { google } from "googleapis";
dotenv.config();

const REDIRECT_URI = `${process.env.FRONTENDURL}/oauth/callback`;
const googleOAuth2Client = new google.auth.OAuth2(
  process.env.GOOGLE_CLIENT_ID,
  process.env.GOOGLE_CLIENT_SECRET,
  REDIRECT_URI
);
if (
  !process.env.GOOGLE_CLIENT_ID ||
  !process.env.GOOGLE_CLIENT_SECRET ||
  !process.env.FRONTENDURL
) {
  throw new Error("Missing necessary Google OAuth environment variables.");
}

export interface GoogleEventsDetails {
  title: string;
  location: string;
  description: string;
  timeZone: string;
  startDateTime: Date; // Assuming it's an ISO string, can also use Date type based on usage
  endDateTime: Date; // Same as above
}

export const addEventToGoogleCalendar = async (
  accessToken: string,
  eventDetails: GoogleEventsDetails,
  eventId?: string
) => {
  googleOAuth2Client.setCredentials({ access_token: accessToken });
  const calendar = google.calendar({ version: "v3", auth: googleOAuth2Client });

  const event = {
    summary: eventDetails.title,
    location: eventDetails.location,
    description: eventDetails.description,
    start: {
      dateTime: eventDetails.startDateTime.toISOString(), // Ensure date type converts to string
      timeZone: eventDetails.timeZone,
    },
    end: {
      dateTime: eventDetails.endDateTime.toISOString(), // Ensure date type converts to string
      timeZone: eventDetails.timeZone,
    },
  };

  try {
    let response;
    if (eventId) {
      response = await calendar.events.update({
        calendarId: "primary",
        eventId,
        requestBody: event,
      });
    } else {
      response = await calendar.events.insert({
        calendarId: "primary",
        requestBody: event,
      });
    }
    console.log("Event created: %s", response.data.htmlLink);
    return { eventId: response.data.id };
  } catch (error: any) {
    console.error("Error creating event:", error.message); // More detailed error message
    throw new Error(`Failed to create event: ${error.message}`);
  }
};

export interface AppleEventDetails {
  start: Date;
  end: Date;
  summary: string;
  description: string;
  location: string;
}

const createEvent = async (args: any) => undefined

/**
 * @dev implement a real createEvent function here, you didnt before
 */
export const addEventToAppleCalendar = async (
  username: string,
  appPassword: string,
  calendarUrl: string,
  eventUrl: string,
  eventData: AppleEventDetails
) => {
  const event = {
    start: new Date(eventData.start),
    end: new Date(eventData.end),
    summary: eventData.summary,
    description: eventData.description,
    location: eventData.location,
  };
  try {
    let response;
    if (eventUrl) {
      response = await createEvent({
        url: eventUrl,
        credentials: { username, password: appPassword },
        event,
        method: "PUT",
      });
    } else {
      response = await createEvent({
        url: calendarUrl,
        credentials: { username, password: appPassword },
        event,
      });
    }
    console.log("Apple Event created: %s", response);
    // return {eventId:response}
  } catch (error: any) {
    console.error("Error creating apple event:", error.message); // More detailed error message
    throw new Error(`Failed to create apple event: ${error.message}`);
  }
};

interface OutlookEvent {
  subject: string;
  body: {
    contentType: "HTML" | "Text";
    content: string;
  };
  start: {
    dateTime: string;
    timeZone: string;
  };
  end: {
    dateTime: string;
    timeZone: string;
  };
  location: {
    displayName: string;
  };
}
interface OutlookEventDetails {
  title: string;
  description: string;
  startDateTime: string;
  endDateTime: string;
  location: string;
  timeZone: string;
}
export const addEventToOutlookCalendar = async (
  accessToken: string,
  eventDetails: OutlookEventDetails
) => {
  const outlookEvent = {
    subject: eventDetails.title,
    body: {
      contentType: "Text",
      content: eventDetails.description,
    },
    start: {
      dateTime: eventDetails.startDateTime,
      timeZone: eventDetails.timeZone,
    },
    end: {
      dateTime: eventDetails.endDateTime,
      timeZone: eventDetails.timeZone,
    },
    location: {
      displayName: eventDetails.location,
    },
  };

  const repsonse = await axios.post(
    "https://graph.microsoft.com/v1.0/me/events",
    outlookEvent,
    {
      headers: {
        Authorization: `Bearer ${accessToken}`,
        "Content-Type": "application/json",
      },
    }
  );
  // return repsonse.data
};
