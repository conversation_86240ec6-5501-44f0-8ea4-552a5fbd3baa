import { Response, NextFunction } from "express";
import User, { IUser } from "../../models/user";
import bcrypt from "bcrypt";
// import jwt from 'jsonwebtoken';
import { createCustomer } from "../../services/stripesService";
import { generateUrlToken } from "../../utils/urlTokenGenrator";
import { isUnwantedInArray, objectKeys, pick } from "../../utils";

export const getUsers = async (
  req: any,
  res: Response,
  next: NextFunction
): Promise<Response> => {
  try {
    const users = await User.find().select("-password");
    return res.status(201).json({ success: true, users });
  } catch (error) {
    next(error);
    console.error("Error adding user:", error);
    return res
      .status(500)
      .json({ success: false, error: "Internal server error" });
  }
};

export const addUser = async (
  req: any,
  res: Response,
  next: NextFunction
): Promise<Response> => {
  try {
    const { first_name, last_name, username, email, password, planId } =
      req.body;
    if (!first_name || !last_name || !username || !email || !password) {
      return res
        .status(400)
        .json({ success: false, error: "All fields are required" });
    }
    const urlToken = generateUrlToken();
    const bookingurl = `${process.env.FRONTENDURL}/bookings/${username}`;
    const existingUser = await User.findOne({ email });
    if (existingUser) {
      return res
        .status(400)
        .json({ success: false, error: "Email is already in use" });
    }
    let userName = `${first_name} ${last_name}`;
    const customer = await createCustomer(userName, email);
    const newUser = new User({
      stripesConsumerId: customer.id,
      first_name,
      last_name,
      username,
      email,
      password,
      urlToken,
      bookingLink: bookingurl,
    });
    await newUser.save();
    return res.status(201).json({
      success: true,
      message: "User added successfully",
      user: newUser,
    });
  } catch (error) {
    next(error);
    console.error("Error adding user:", error);
    return res
      .status(500)
      .json({ success: false, error: "Internal server error" });
  }
};

export const editUser = async (
  req: any,
  res: Response,
  next: NextFunction
): Promise<Response> => {
  try {
    const toBeUpdated = pick(
      req.body as IUser,
      "first_name",
      "last_name",
      "username",
      "password",
      "email"
    );
    const user = await User.findOne({ email: toBeUpdated.email });
    if (!user)
      return res.status(404).json({ success: false, error: "User not found" });
    objectKeys(toBeUpdated).forEach((key) => {
      const value = toBeUpdated[key];
      if ([undefined, null, ""].includes(value)) return;
      user[key] = value;
    });
    await user.save();
    return res
      .status(200)
      .json({ success: true, message: "User updated successfully" });
  } catch (error) {
    next(error);
    console.error("Error adding user:", error);
    return res
      .status(500)
      .json({ success: false, error: "Internal server error" });
  }
};

export const deleteUser = async (
  req: any,
  res: Response,
  next: NextFunction
): Promise<Response> => {
  try {
    const { userId } = req.params;

    if (!userId) {
      return res
        .status(400)
        .json({ success: false, error: "User ID is required" });
    }

    const deletedUser = await User.findByIdAndUpdate(
      userId,
      { isDeleted: true },
      { new: true }
    );
    if (!deletedUser) {
      return res.status(404).json({ success: false, error: "User not found" });
    }

    return res
      .status(200)
      .json({ success: true, message: "User deleted successfully" });
  } catch (error) {
    next(error);
    console.error("Error deleting user:", error);
    return res.status(500).json({ message: "Internal server error" });
  }
};

export const resetPassword = async (
  req: any,
  res: Response,
  next: NextFunction
): Promise<Response> => {
  try {
    const { userId } = req.params;
    const { newPassword } = req.body;

    if (!newPassword) {
      return res
        .status(400)
        .json({ success: false, error: "New password is required" });
    }

    const hashedPassword = await bcrypt.hash(newPassword, 10);
    const updatedUser = await User.findByIdAndUpdate(
      userId,
      { password: hashedPassword },
      { new: true }
    );

    if (!updatedUser) {
      return res.status(404).json({ success: false, error: "User not found" });
    }

    return res
      .status(200)
      .json({ success: true, message: "Password reset successfully" });
  } catch (error) {
    next(error);
    console.error("Error resetting password:", error);
    return res
      .status(500)
      .json({ success: false, error: "Internal server error" });
  }
};

export const toggleUserStatus = async (
  req: any,
  res: Response,
  next: NextFunction
): Promise<Response> => {
  try {
    const { userId } = req.params;
    const { isActive } = req.body;

    if (typeof isActive !== "boolean") {
      return res.status(400).json({ error: "Invalid status value" });
    }

    if (!userId) {
      return res.status(400).json({ error: "User ID is required" });
    }
    const updatedUser = await User.findByIdAndUpdate(
      userId,
      { isDeactivated: isActive },
      { new: true }
    );

    if (!updatedUser) {
      return res.status(404).json({ error: "User not found" });
    }

    const statusMessage = isActive
      ? "User account activated successfully"
      : "User account deactivated successfully";

    return res.status(200).json({ success: true, message: statusMessage });
  } catch (error) {
    console.error("Error updating user status:", error);
    return res.status(500).json({ message: "Internal server error" });
  }
};
