import { NextFunction, Request, Response, response } from "express";
import mongoose, { Types } from "mongoose";
import puzzlePrice from "../models/admin/puzzlePrice";
import PuzzleProduct from "../models/admin/puzzleProduct";
import paymentMethod from "../models/paymentMethod";
import User from "../models/user";
import userSubscription from "../models/userSubscription";
import {
  CreateStripeSubscription,
  UpdateStripeSubscription,
  attachPaymentDefaultMethodtoCustomer,
  attachPaymentMethodtoCustomer,
  createSetupIntent,
  retrieveInvoice,
  retrievePaymentIntentofInvoice,
  retrieveSubscription,
} from "../services/stripesService";
import { getObjectIdString, inlineSwitch } from "../utils";
import PuzzlePrice from "../models/admin/puzzlePrice";

interface ProductResponse {
  // _id:string;
  name: string;
  price: number;
  description: string;
  recurring: "weekly" | "monthly" | "yearly";
  discount?: {
    id: string;
    name: string;
    coupon_code: string;
  };
  features: string[];
  yearlyPrice: number;
}

export const GetSubscriptionPlans = async (
  req: Request,
  res: Response
): Promise<Response> => {
  try {
    const products = await PuzzleProduct.find({ active: true });
    const productPrices = await puzzlePrice.find();
    const productList: ProductResponse[] = products.map((product) => {
      const productId = product._id as Types.ObjectId;
      const thisProductPrices = productPrices.filter(price => price.productId.equals(productId));
      const { monthlyPrice, yearlyPrice } = (() => {
        return {
          monthlyPrice: thisProductPrices.find((price) => price.interval === 'month'),
          yearlyPrice: thisProductPrices.find((price) => price.interval === 'year'),
        }
      })()
      if (!monthlyPrice && !yearlyPrice) {
        console.warn(`No price found for product ID: ${product._id}`);
      }
      return {
        _id: product._id,
        name: product.name,
        price: monthlyPrice?.unitAmount || 0,
        description: product.description || "",
        yearlyPrice: yearlyPrice?.unitAmount || 0,
        recurring: 'monthly',
        //   discount: {
        //     id: 'discountId123', // Example data or retrieve dynamically
        //     name: 'Seasonal Discount',
        //     coupon_code: 'SUMMER20' // Example data or retrieve dynamically
        //   },
        features: product.features,
      };
    });
    return res.status(200).json({ plans: productList });
  } catch (error) {
    console.error("Error fetching products:", error);
    return res.status(500).json({ error: "Failed to fetch products" });
  }
};

export const GetCurrentSubscriptionPlan = async (
  req: any,
  res: Response
): Promise<Response> => {
  try {
    const planId = req?.user?.planId;
    const products = await PuzzleProduct.find({ active: true });
    const prices = await PuzzlePrice.find();
    const [userSelectedPlan, userSelectedPlanPrice, userSelectedPlanYearlyPrice] = [
      products.find((product) => getObjectIdString(product._id) === planId),
      prices.find((productPrice) => getObjectIdString(productPrice.productId) === planId),
      prices.find((productPrice) => (
        productPrice.interval === 'year' &&
        getObjectIdString(productPrice.productId) === planId
      )),
    ];
    if (!userSelectedPlan) return res.status(404).json({
      error: 'Subscription plan not found'
    });
    const userPlan: ProductResponse & Record<string, any> = {
      _id: userSelectedPlan?._id,
      name: userSelectedPlan.name,
      price: userSelectedPlanPrice?.unitAmount || 0,
      description: userSelectedPlan.description || "",
      yearlyPrice: userSelectedPlanYearlyPrice?.unitAmount || 0,
      recurring:
        inlineSwitch(
          userSelectedPlanPrice!.interval,
          ["week", "weekly"],
          ["month", "monthly"],
          ["year", "yearly"]
        ) || "monthly",
      features: userSelectedPlan.features,
    };
    return res.status(200).json({ plan: userPlan });
  } catch (error) {
    console.error("Error fetching products:", error);
    return res.status(500).json({ error: "Failed to fetch products" });
  }
};

export const upgradeSubscription = async (
  req: any,
  res: Response,
  next: NextFunction
): Promise<Response> => {
  try {
    const userId = req.user?._id;
    const { planId } = req.body;
    const user = await User.findById(userId);
    if (!user || !user.stripesConsumerId) {
      return res
        .status(404)
        .json({ error: "User not found or missing Stripe consumer ID" });
    }
    const subscriptionPrice = await puzzlePrice.findOne({ productId: planId });
    if (!subscriptionPrice || !subscriptionPrice.stripePriceId) {
      return res.status(404).json({ error: "Subscription Plan not found" });
    }
    const UserSub = await userSubscription.findOne({ user: userId });
    if (UserSub) {
      const updatedSubscription = await UpdateStripeSubscription(
        UserSub.stripeSubscriptionId,
        subscriptionPrice.stripePriceId
      );
      UserSub.subscriptionPlan =
        subscriptionPrice._id as mongoose.Types.ObjectId;
      UserSub.status = updatedSubscription.status;
      UserSub.startDate = new Date(
        updatedSubscription.current_period_start * 1000
      );
      UserSub.endDate = new Date(updatedSubscription.current_period_end * 1000);
      UserSub.nextBillingDate = new Date(
        updatedSubscription.current_period_end * 1000
      );

      const latestInvoice = updatedSubscription.latest_invoice;
      let clientSecret: string | undefined | null;
      if (
        typeof latestInvoice !== "string" &&
        typeof latestInvoice?.payment_intent !== "string"
      ) {
        const paymentIntentId = latestInvoice?.payment_intent?.id;
        console.log("paymentIntentId", paymentIntentId);
        clientSecret = latestInvoice?.payment_intent?.client_secret;
      }
      await UserSub.save();
      // save new plan id for user, can be used to fetch users current plan for ui
      user.planId = planId;
      await user.save();
      return res.status(200).json({
        success: true,
        message: "Subscription upgraded successfully",
        subscription: UserSub,
        clientSecret: clientSecret,
      });
    } else {
      return res
        .status(500)
        .json({ error: "Failed to update subscription, try again" });
    }
  } catch (error) {
    next(error);
    console.error("Error upgrading subscription:", error);
    return res.status(500).json({ error: "Failed to upgrade subscription" });
  }
};

const addNewDefaultPaymethod = async (
  consumerId: string,
  paymentMethodId: string
) => {
  try {
    const user = await User.findOne({ stripesConsumerId: consumerId });

    if (!user) {
      return {
        status: 404,
        success: false,
        error: "User not found",
      };
    }

    // Attach payment method to the Stripe customer
    const StripepaymentMethod = await attachPaymentDefaultMethodtoCustomer(
      user.stripesConsumerId,
      paymentMethodId
    );
    if (!StripepaymentMethod) {
      return {
        status: 500,
        success: false,
        error: "Failed to attach payment method to Stripe customer",
      };
    }
    await paymentMethod.findOneAndUpdate(
      { userId: user._id, type: "default" },
      { type: "others" }
    );
    const newPayMethod = new paymentMethod({
      userId: user._id,
      type: "default",
      stripePaymethodId: StripepaymentMethod.id,
      provider: StripepaymentMethod.card?.brand,
      last4Digits: StripepaymentMethod.card?.last4,
      expiryMonth: StripepaymentMethod.card?.exp_month,
      expiryYear: StripepaymentMethod.card?.exp_year,
    });
    await newPayMethod.save();
    return {
      status: 200,
      success: true,
      message: "Default payment method successfully updated",
    };
  } catch (error: any) {
    return {
      status: 500,
      success: false,
      error: `An unexpected error occurred: ${error.message || error}`,
    };
  }
};

//  // Call the function with the request data
//  const result = await addNewDefaultPaymethod(consumerId, paymentMethodId);

//  // Return response based on the function's returned result
//  return res.status(result.status).json({
//    success: result.success,
//    message: result.message,
//    error: result.error
//  });

/**
 * Create a new subscription for a user on Stripe and save in the database.
 * @param userId - The ID of the user in your database.
 * @param userSripesConsumerId - The Stripe customer ID for the user.
 * @param planId - The subscription plan ID for the selected plan.'
 *
 */
export const createUserSubscription = async (
  userId: string,
  userStripesConsumerId: string,
  planId: string
) => {
  try {
    const existingSubscription = await userSubscription.findOne({
      user: userId,
    });
    if (existingSubscription) {
      const subStatus = await checkSubscriptionStatus(userId);
      if (subStatus.status === "incomplete") {
        return {
          message: subStatus.message,
          clientSecret: subStatus.clientSecret,
          Usersubscription: existingSubscription,
        };
      }
      return {
        message: subStatus.message,
        Usersubscription: existingSubscription,
      };
    }

    const subscriptionPrice = await puzzlePrice.findOne({ productId: planId });
    if (!subscriptionPrice || !subscriptionPrice.stripePriceId) {
      throw new Error("Price not found for the specified plan");
    }
    const newSubscription = await CreateStripeSubscription(
      userStripesConsumerId,
      subscriptionPrice.stripePriceId
    );

    const newUserSub = new userSubscription({
      user: userId,
      subscriptionPlan: subscriptionPrice._id,
      stripeSubscriptionId: newSubscription.id,
      status: newSubscription.status,
      startDate: new Date(newSubscription.current_period_start * 1000),
      endDate: new Date(newSubscription.current_period_end * 1000),
      nextBillingDate: new Date(newSubscription.current_period_end * 1000),
    });
    await newUserSub.save();
    const latestInvoice = newSubscription.latest_invoice;
    let clientSecret: string | undefined | null;
    if (
      typeof latestInvoice !== "string" &&
      typeof latestInvoice?.payment_intent !== "string"
    ) {
      const paymentIntentId = latestInvoice?.payment_intent?.id;
      console.log("paymentIntentId", paymentIntentId);
      clientSecret = latestInvoice?.payment_intent?.client_secret;
    }
    return {
      message: "Subscription created successfully",
      Usersubscription: newUserSub,
      clientSecret,
    };
  } catch (error) {
    console.error("Error in createSubscription:", error);
    throw new Error(`Subscription creation or update failed: ${error}`);
  }
};

export const UpgradeUserSubscription = async (
  userId: string,
  planId: string
) => {
  try {
    const user = await User.findById(userId);
    if (!user || !user.stripesConsumerId) {
      throw new Error("User or Stripe customer not found");
    }
    const subscriptionPrice = await puzzlePrice.findOne({ productId: planId });
    if (!subscriptionPrice || !subscriptionPrice.stripePriceId) {
      throw new Error("Price not found for the specified plan");
    }
    const UserSub = await userSubscription.findOne({ user: userId });
    if (UserSub) {
      const updatedSubscription = await UpdateStripeSubscription(
        UserSub.stripeSubscriptionId,
        subscriptionPrice.stripePriceId
      );
      UserSub.subscriptionPlan =
        subscriptionPrice._id as mongoose.Types.ObjectId;
      UserSub.status = updatedSubscription.status;
      UserSub.startDate = new Date(
        updatedSubscription.current_period_start * 1000
      );
      UserSub.endDate = new Date(updatedSubscription.current_period_end * 1000);
      UserSub.nextBillingDate = new Date(
        updatedSubscription.current_period_end * 1000
      );
      await UserSub.save();

      return {
        message: "Subscription updated successfully",
        Usersubscription: UserSub,
      };
    } else {
      throw new Error("Failed to update subscription");
    }
  } catch (error) {
    console.error("Error in createOrUpdateSubscription:", error);
    throw new Error(`Subscription creation or update failed: ${error}`);
  }
};

/**
 * Get the client secret for an unpaid invoice.
 * @param userId - userId
 * @returns {Promise<{ status:'incomplete'| 'incomplete_expired'| 'trialing'| 'active'| 'past_due'|'canceled'|'unpaid'|'paused', clientSecret: string | null, message: string }>}
 */
export async function checkSubscriptionStatus(userId: string): Promise<{
  status:
    | "incomplete"
    | "incomplete_expired"
    | "trialing"
    | "active"
    | "past_due"
    | "canceled"
    | "unpaid"
    | "paused";
  clientSecret: string | null;
  message: string;
} > {
  try {
    const userSub = await userSubscription.findOne({ user: userId });
    if (!userSub) {
      throw new Error("User subscription not found.");
    }

    const stripeSubscriptionId = userSub.stripeSubscriptionId;
    const stripeSubscription = await retrieveSubscription(stripeSubscriptionId);

    let updatedStatus = "inactive";
    let clientSecret = null;
    let message = "Subscription status updated.";

    switch (stripeSubscription.status) {
      case "active":
        updatedStatus = "active";
        message = "Subscription is active.";
        break;

      case "trialing":
        updatedStatus = "trialing";
        message = "Subscription is currently in trial period.";
        break;

      case "past_due":
        updatedStatus = "past_due";
        message =
          "Subscription payment is past due. Please update payment method.";
        break;

      case "canceled":
        updatedStatus = "canceled";
        message = "Subscription has been canceled.";
        break;

      case "unpaid":
        updatedStatus = "unpaid";
        message =
          "Subscription is unpaid. Please complete payment to reactivate.";
        break;

      case "incomplete":
        const latestInvoiceId = stripeSubscription.latest_invoice as string;
        const stripeInvoice = await retrieveInvoice(latestInvoiceId);
        if (stripeInvoice.status === "open" && stripeInvoice.payment_intent) {
          const paymentIntent = await retrievePaymentIntentofInvoice(
            stripeInvoice.payment_intent as string
          );
          clientSecret = paymentIntent.client_secret;
          message =
            "Please complete your payment to activate your subscription.";
        }
        break;
      default:
        message = "Subscription status unknown. Please contact support.";
    }
    await userSubscription.findOneAndUpdate(
      { user: userId },
      {
        status: updatedStatus,
        startDate: new Date(stripeSubscription.start_date * 1000),
        endDate: new Date(stripeSubscription.current_period_end * 1000),
        nextBillingDate: stripeSubscription.current_period_end
          ? new Date(stripeSubscription.current_period_end * 1000)
          : null,
      }
    );
    return {
      status: stripeSubscription.status,
      clientSecret,
      message,
    };
  } catch (error: unknown) {
    console.error("Error retrieving unpaid invoice client secret:", error instanceof Error ? error.message : String(error));
    throw error;
  }
}
export const SetupIntent = async (
  req: any,
  res: Response
): Promise<Response> => {
  const userId = req.user._id;
  try {
    const user = await User.findById(userId);
    if (!user) {
      return response.status(404).json({
        success: false,
        error: "User not found",
      });
    }
    const clientSecret = await createSetupIntent(user.stripesConsumerId);
    return res.status(200).json({
      success: true,
      message: "Intent setup successful",
      clientSecret,
    });
  } catch (error) {
    console.error("Error fetching products:", error);
    return res.status(500).json({ error: "Failed to fetch products" });
  }
};
export const AddPaymentMethod = async (
  req: any,
  res: Response
): Promise<Response> => {
  const userId = req.user._id;
  const { paymentId, type } = req.body;
  try {
    const user = await User.findById(userId);
    if (!user) {
      return response.status(404).json({
        success: false,
        error: "User not found",
      });
    }
    if (type === "default") {
      const payresult = await addNewDefaultPaymethod(
        user.stripesConsumerId,
        paymentId
      );
      return res.status(payresult.status).json({
        success: payresult.success,
        message: payresult.message,
        error: payresult?.error,
      });
    } else {
      const StripepaymentMethod = await attachPaymentMethodtoCustomer(
        user.stripesConsumerId,
        paymentId
      );
      const newPayMethod = new paymentMethod({
        userId: user?._id,
        type,
        stripePaymethodId: StripepaymentMethod.id,
        provider: StripepaymentMethod.card?.brand,
        last4Digits: StripepaymentMethod.card?.last4,
        expiryMonth: StripepaymentMethod.card?.exp_month,
        expiryYear: StripepaymentMethod.card?.exp_year,
      });
      await newPayMethod.save();
      return res.status(200).json({
        success: true,
        message: "Attached payment method sucessfully",
        newPayMethod,
      });
    }
  } catch (error) {
    console.error("Error adding payment Method:", error);
    return res.status(500).json({ error: "Failed to fetch products" });
  }
};

export const AddPaymentMethodOnSignup = async (
  req: any,
  res: Response
): Promise<Response> => {
  const { userId, paymentId, type } = req.body;
  try {
    const user = await User.findById(userId);
    if (!user) {
      return response.status(404).json({
        success: false,
        error: "User not found",
      });
    }
    if (type === "default") {
      const payresult = await addNewDefaultPaymethod(
        user.stripesConsumerId,
        paymentId
      );
      return res.status(payresult.status).json({
        success: payresult.success,
        message: payresult.message,
        error: payresult?.error,
      });
    } else {
      return res.status(400).json({
        success: false,
        error: "payment type must be default",
      });
    }
  } catch (error) {
    console.error("error on saving signup payment method:", error);
    return res.status(500).json({ error: "Failed to fetch products" });
  }
};
