# Build stage
FROM node:20-alpine AS builder

# Install build dependencies
RUN apk add --no-cache python3 make g++ gcc
WORKDIR /app
COPY --chown=node:node package*.json ./

# Install all dependencies including devDependencies
RUN npm ci

# Copy source code
COPY --chown=node:node . .

# Build TypeScript code
RUN npm run build

# Production stage
FROM node:20-alpine

# Install production dependencies
RUN apk add --no-cache python3 make g++

# Create app directory and set ownership
WORKDIR /app
RUN chown -R node:node /app

USER node

# Copy package files
COPY --chown=node:node package*.json ./
COPY --chown=node:node . .
RUN npm ci ts-node
# Copy built files from builder stage
COPY --chown=node:node --from=builder /app/dist ./dist

RUN npm cache clean --force
# Expose port
EXPOSE 5002

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=30s \
    CMD wget -q --spider http://localhost:${PORT}/health || exit 1

# Start the application
# CMD ["node", "dist/index.js"]
CMD ["npm", "start"]