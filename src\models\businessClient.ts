import mongoose, { Schema, Document } from 'mongoose';

export interface IBusinessClient extends Document {
  userId: mongoose.Types.ObjectId;
  clientName:string;
  clientEmail:string;
  phoneNumber:string;
  lastAppointmentDate?:Date;
  totalAppointments:number;
}

const businessClientSchema: Schema = new Schema({
  userId: { type: mongoose.Types.ObjectId, required: true, ref: 'User' },
  clientName: { type: String,required: true },
  clientEmail: { type: String,required: true },
  phoneNumber: { type: String,required: true },
  lastAppointmentDate: {
    type: Date, 
    default: null
  },
  totalAppointments: { type: Number, default: 0 },
}, { timestamps: true });
businessClientSchema.index({ userId: 1, clientEmail: 1 }, { unique: true });
export default mongoose.model<IBusinessClient>('BusinessClient', businessClientSchema);
