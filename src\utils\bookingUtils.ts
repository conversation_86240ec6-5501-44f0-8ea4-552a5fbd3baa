export function addDurationToTime(timeStr:string, duration:number) {
    // Step 1: Parse the time
    const [hours, minutes] = timeStr.split(':').map(Number);

    // Step 2: Convert to total minutes
    let totalMinutes = hours * 60 + minutes;

    // Step 3: Add duration
    totalMinutes += duration;

    // Step 4: Convert back to hh:mm
    const newHours = Math.floor(totalMinutes / 60) % 24;
    const newMinutes = totalMinutes % 60;

    // Format as "hh:mm" with leading zeros
    return `${String(newHours).padStart(2, '0')}:${String(newMinutes).padStart(2, '0')}`;
}
export function combineDateAndTime(dateStr: string, timeStr: string): Date {
    const [hours, minutes] = timeStr.split(':').map(Number);
    const date = new Date(dateStr);

    date.setUTCHours(hours, minutes, 0, 0); // UTC mode for Z (Zulu) or timezone aware handling
    return date;
}