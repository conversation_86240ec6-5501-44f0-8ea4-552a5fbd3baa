import { Request, Response, NextFunction } from 'express';

const requestLogger = (req: Request, res: Response, next: NextFunction): void => {
  const start = process.hrtime();

  res.on('finish', () => {
    const diff = process.hrtime(start);
    const time = diff[0] * 1e3 + diff[1] * 1e-6; // Convert to milliseconds
    console.log(`${req.method} ${req.originalUrl} [${res.statusCode}] - ${time.toFixed(3)} ms`);
  });

  next();
};

export default requestLogger;
