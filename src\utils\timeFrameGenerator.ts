/**
 * Generates time frames based on the specified interval
 * @param timeInterval - The time interval ('today', 'weekly', 'monthly', 'yearly')
 * @returns Array of time frames with label, start, and end dates
 */
export const generateTimeFrames = (timeInterval: 'today'| 'weekly' | 'monthly' | 'yearly'): Array<{ label: string, start: Date, end: Date }> => {
    const now = new Date();
    const currentYear = now.getFullYear();
    const timeFrames: Array<{ label: string, start: Date, end: Date }> = [];
  
    if (timeInterval === 'today') {
      // Just one entry for today
      const startOfDay = new Date(now.getFullYear(), now.getMonth(), now.getDate());
      const endOfDay = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 23, 59, 59);
      timeFrames.push({ 
        label: 'Today', 
        start: startOfDay, 
        end: endOfDay 
      });
    } 
    else if (timeInterval === 'weekly') {
      // Data for each day of current week
      const dayOfWeek = now.getDay(); // 0 = Sunday, 1 = Monday, etc.
      const diff = dayOfWeek === 0 ? 6 : dayOfWeek - 1; // Adjust to get Monday
      const startOfWeek = new Date(now);
      startOfWeek.setDate(now.getDate() - diff);
      startOfWeek.setHours(0, 0, 0, 0);
      
      const dayNames = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];
      
      for (let i = 0; i < 7; i++) {
        const dayStart = new Date(startOfWeek);
        dayStart.setDate(startOfWeek.getDate() + i);
        dayStart.setHours(0, 0, 0, 0);
        
        const dayEnd = new Date(dayStart);
        dayEnd.setHours(23, 59, 59, 999);
        
        timeFrames.push({
          label: dayNames[i],
          start: dayStart,
          end: dayEnd
        });
      }
    } 
    else if (timeInterval === 'monthly') {
      // Data for each month of current year
      const monthNames = [
        'January', 'February', 'March', 'April', 'May', 'June',
        'July', 'August', 'September', 'October', 'November', 'December'
      ];
      
      for (let month = 0; month < 12; month++) {
        const startOfMonth = new Date(currentYear, month, 1);
        const endOfMonth = new Date(currentYear, month + 1, 0, 23, 59, 59);
        
        timeFrames.push({
          label: monthNames[month],
          start: startOfMonth,
          end: endOfMonth
        });
      }
    } 
    else if (timeInterval === 'yearly') {
      // Data for each of the last 6 years
      for (let i = 5; i >= 0; i--) {
        const year = currentYear - i;
        const startOfYear = new Date(year, 0, 1);
        const endOfYear = new Date(year, 11, 31, 23, 59, 59);
        
        timeFrames.push({
          label: year.toString(),
          start: startOfYear,
          end: endOfYear
        });
      }
    }
    
    return timeFrames;
  };
  