import { Request, Response, NextFunction } from "express";
import maintenanceMode from "../../models/admin/maintenanceMode";

export const getMaintenanceMode = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<Response> => {
  try {
    const maintenance = await maintenanceMode.findOne();
    if (!maintenance) {
      return res.status(404).json({ error: "Maintenance mode not found" });
    }
    return res.status(200).json({
      maintenance
    });
  } catch (error) {
    next(error);
    console.error("Error fetching maintenance mode:", error);
    return res.status(500).json({ error: "Failed to fetch maintenance mode" });
  }
};

export const setMaintenanceMode = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<Response> => {
  try {
    const { isActive, message } = req.body;
    let maintenance = await maintenanceMode.findOne();
    if (!maintenance) {
      // If no document exists, create one
      maintenance = new maintenanceMode({ isActive, message });
    } else {
      // Update the existing document
      maintenance.isActive = isActive;
      maintenance.message = message;
    }
    await maintenance.save();
    return res
      .status(200)
      .json({ message: "Maintenance mode updated successfully" });
  } catch (error) {
    next(error);
    console.error("Error updating maintenance mode:", error);
    return res.status(500).json({ error: "Failed to update maintenance mode" });
  }
};

/**
 * @dev to be used by the main app when our site is under maintenance.
 */
export const checkMaintenanceMode = async (
  req: any,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const Maintenance = await maintenanceMode.findOne();
    if (Maintenance && Maintenance.isActive) {
      const user = req.user;
      if (!user || user.role !== "admin") {
        res.status(503).json({
          message: "The site is under maintenance.",
          customMessage: Maintenance.message,
        });
      }
    }
    next();
  } catch (error) {
    if (error instanceof Error) {
      res.status(500).json({ error: error.message });
    } else {
      res.status(500).json({ error: "An unknown error occurred" });
    }
  }
};
