import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import User, { IUser } from '../models/user'; // Adjust import if necessary
const secretKey =process.env.JSONSECRETKEY || 'your_jwt_secret'

export const authMiddleware = async (req: any, res: Response, next: NextFunction):Promise<Response | void> => {
    const authHeader = req.headers.authorization;
    
    if (!authHeader) {
        return res.sendStatus(401); // Unauthorized if no token is provided
    }
    const token = authHeader.split(' ')[1];
    try {
        const PAYLOAD = jwt.verify(token, secretKey as string) as any;

        if (!PAYLOAD) {
            return res.sendStatus(403); // Forbidden if payload is invalid
        }

        const user = await User.findById(PAYLOAD.userId);
        if (!user) {
            return res.sendStatus(404); // Not Found if user doesn't exist
        }

        req.user = user; 
        next();
    } catch (err:any) {
        // Log the error for debugging purposes
        console.error('Authentication error:', err);
        return res.status(403).json({ error: 'Forbidden', details: err?.message }); // Return error details in JSON
    }
};

export function authorizeRoles(roles:string[] = []) {
    return (req: any, res: Response, next: NextFunction) => {
      const user:IUser = req.user;
      if (!roles.includes(user.role)) {
        return res.status(403).json({ message: 'Access forbidden: insufficient permissions' });
      }
  
      next();
    };
  }
