import { Request, Response, NextFunction } from "express";
import { BlockedIP } from "../models/resources";

const blockSuspiciousIPs = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const clientIP = req.ip || req.headers["x-forwarded-for"] || req.socket.remoteAddress;

    // Check if the IP exists in the database
    const blocked = await BlockedIP.findOne({ ip: clientIP });
    if (blocked) {
      console.log(`Blocked access for IP: ${clientIP}`);
      return res.status(404).send("Not Found");
    }

    next(); // Proceed to the next middleware if not blocked
  } catch (error) {
    console.error("Error in blockSuspiciousIPs middleware:", error);
    next(error);
  }
};

export default blockSuspiciousIPs;
