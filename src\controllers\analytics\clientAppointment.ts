import {  Response, NextFunction } from 'express';
import clientAppointment from '../../models/clientAppointment';
import services from '../../models/services';
import businessClient from '../../models/businessClient';
import { generateTimeFrames } from '../../utils/timeFrameGenerator';
import mongoose from 'mongoose';



export const GetTodayAppointmentRenvenue = async (req: any, res: Response, next: NextFunction): Promise<Response> => {
    try {
        const userId = req.user._id
        const startOfDay = new Date();
        startOfDay.setHours(0, 0, 0, 0);
    
        const endOfDay = new Date();
        endOfDay.setHours(23, 59, 59, 999);
    
        const totalRevenueResult = await clientAppointment.aggregate([
          {
            $match: {
                userId:userId,
                date: { $gte: startOfDay, $lt: endOfDay },
                status: 'completed'
            }
          },
          {
            $group: {
              _id: null,
              totalRevenue: { $sum: "$revenue" }
            }
          }
        ]);
    
        const totalRevenue = totalRevenueResult.length > 0 ? totalRevenueResult[0].totalRevenue : 0;
    
        return res.status(200).json({ totalRevenue });
      } catch (error) {
        console.error('Error fetching total revenue:', error);
        return res.status(500).json({ error: 'Internal server error' });
      }
}

export const GetCurrentMonthAppointmentRevenue = async (req: any, res: Response, next: NextFunction): Promise<Response> => {
    try {
        const userId = req.user._id
    
        const now = new Date();
        const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
        const startOfNextMonth = new Date(now.getFullYear(), now.getMonth() + 1, 1).getTime();
        const endOfMonth = new Date(startOfNextMonth - 1);
    
        const totalRevenueResult = await clientAppointment.aggregate([
          {
            $match: {
              userId:userId,
              date: { $gte: startOfMonth, $lte: endOfMonth },
              status: 'completed'
            }
          },
          {
            $group: {
              _id: null,
              totalRevenue: { $sum: "$revenue" }
            }
          }
        ]);
    
        const totalRevenue = totalRevenueResult.length > 0 ? totalRevenueResult[0].totalRevenue : 0;
    
        return res.status(200).json({ totalRevenue });
      } catch (error) {
        console.error('Error fetching total monthly revenue:', error);
        return res.status(500).json({ message: 'Internal server error' });
      }
}


export const GetAppointmentReturnRate  = async (req: any, res: Response, next: NextFunction): Promise<Response> => {
    try {
        const {timeInterval} = req.query
        const {serviceId} = req.params
        const userId = req.user._id
        const service = await services.findOne({_id:serviceId, userId}).select('name')
        if(!service) return res.status(404).json({error:'Service not found'})

        let serviceData = {
            serviceId: service._id,
            serviceName: service.name,
            returnRates: [] as any[]
        };
        const timeFrames: { start: Date; end: Date; label: string }[] = generateTimeFrames(timeInterval|| 'today')
        for (const timeFrame of timeFrames) {
          const serviceAppointments = await clientAppointment.find({
            service:service._id,
            userId:userId,
            date: { $gte: timeFrame.start, $lte: timeFrame.end }
          });
          const clientAppointmentCounts:any = {};
          serviceAppointments.forEach((appointment) => {
            const clientId:string = appointment.clientId?.toString() as string;
            if (!clientAppointmentCounts[clientId]) {
                clientAppointmentCounts[clientId] = 0;
            }
            clientAppointmentCounts[clientId]++;
          });
          const totalClients = Object.keys(clientAppointmentCounts).length;
          const totalReturningClients = Object.values(clientAppointmentCounts)
          .filter(count => (count as number) > 1).length;
          const returnRate = totalClients > 0 
            ? ((totalReturningClients / totalClients) * 100).toFixed(2) 
            : "0.00";
          serviceData.returnRates.push({
            timeFrame: timeFrame.label,
            totalClients,
            totalReturningClients,
            returnRate: `${returnRate}%`
          });
        }
        return res.status(200).json(serviceData);
    } catch (error) {
        console.error('Error calculating service return rates:', error);
        return res.status(500).json({ error: 'Internal server error' });
    }

};
export const getMostUsedServices  = async (req: any, res: Response, next: NextFunction): Promise<Response> => {
  try {
      const {timeInterval} = req.query
     const userId = req.user._id
     const timeFrames = generateTimeFrames(timeInterval || 'today')
     const mostUsedServices:any = []
     for (const timeFrame of timeFrames) {
        const mostUsedService = await clientAppointment.aggregate([
          { $match: {userId, date: { $gte: timeFrame.start, $lte: timeFrame.end }} },
          { $group: { _id: "$service", count: { $sum: 1 } } },
          { $sort: { count: -1 } },
          { $limit: 1 }
        ]);
        if (mostUsedService.length) {
          const serviceDetails = await services.findById(mostUsedService[0]._id);
          if (!serviceDetails) {
            return res.status(404).json({error: 'Service details not found.' });
          }
          if (serviceDetails) {
            mostUsedServices.push({
              timeFrame: timeFrame.label,
              service: serviceDetails,
              count: mostUsedService[0].count
            });
          }
        }
      }

      return res.status(200).json({
          message: 'Most used service retrieved successfully.',
          mostUsedServices
      });

  } catch (error) {
      console.error('Error getting most used service:', error);
      return res.status(500).json({ error: 'Internal server error.' });
  }
};

export const getClientCountAndPercentage = async (req: any, res: Response, next: NextFunction): Promise<Response> => {
  try {
    const { timeInterval } = req.query
    const userId = req.user._id
    const { serviceId } = req.params
    
    const service = await services.findOne({ _id: serviceId, userId });
    if (!service) {
      return res.status(404).json({ error: 'Service not found.' });
    }
    
    const totalClients = await businessClient.find({ userId });
    if (!totalClients.length) {
      return res.status(404).json({ error: 'No client data found for this user.' });
    }
    
    const timeFrames = generateTimeFrames(timeInterval || 'today');

    const data = [];
    for (const timeFrame of timeFrames) {
      const clientCount = {
        timeFrame: timeFrame.label,
        serviceName: service.name,
        clientCount: 0,
        percentage: "0.00%"
      };
      
      // Updated query to filter by the specific serviceId
      const clientCountResult = await clientAppointment.aggregate([
        {
          $match: {
            userId,
            service: service._id,
            date: { $gte: timeFrame.start, $lte: timeFrame.end }
          }
        },
        {
          $group: {
            _id: "$clientId",
            count: { $sum: 1 }
          }
        },
        {
          $group: {
            _id: null,
            clientCount: { $sum: 1 }
          }
        }
      ]);
      
      if (clientCountResult.length) {
        clientCount.clientCount = clientCountResult[0].clientCount;
        const percentage = (clientCount.clientCount / totalClients.length) * 100;
        clientCount.percentage = percentage.toFixed(2) + "%";
      }
      
      data.push(clientCount);
    }

    return res.status(200).json({
      message: 'Client count and percentage for the service retrieved successfully.',
      serviceName: service.name,
      serviceId: service._id,
      data
    });
    
  } catch (error) {
    console.error('Error getting client count and percentage:', error);
    return res.status(500).json({ error: 'Internal server error.' });
  }
};

export const getAverageRevenue = async (req: any, res: Response, next: NextFunction): Promise<Response> => {
  try {
    const userId = req.user?._id;
    const { serviceId } = req.params;
    const { timeInterval } = req.query;

    // Verify the service exists and belongs to the user
    const service = await services.findOne({ _id: serviceId, userId });
    if (!service) {
      return res.status(404).json({ error: 'Service not found or does not belong to this user.' });
    }

    // Generate time frames based on the requested interval
    const timeFrames = generateTimeFrames(timeInterval || 'today');
    
    const data = [];
    let totalRevenue = 0;
    let totalAppointments = 0;

    for (const timeFrame of timeFrames) {
      // Query appointments for this service in the current time frame
      const appointmentResult = await clientAppointment.aggregate([
        {
          $match: {
            userId: userId,
            service: service._id,
            status: 'completed',
            date: { $gte: timeFrame.start, $lte: timeFrame.end }
          }
        },
        {
          $group: {
            _id: null,
            totalRevenue: { $sum: '$revenue' },
            appointmentCount: { $sum: 1 }
          }
        }
      ]);

      const timeFrameData = {
        timeFrame: timeFrame.label,
        serviceName: service.name,
        totalRevenue: 0,
        appointmentCount: 0,
        averageRevenue: 0
      };

      if (appointmentResult.length > 0) {
        timeFrameData.totalRevenue = appointmentResult[0].totalRevenue;
        timeFrameData.appointmentCount = appointmentResult[0].appointmentCount;
        timeFrameData.averageRevenue = timeFrameData.appointmentCount > 0 
          ? timeFrameData.totalRevenue / timeFrameData.appointmentCount 
          : 0;
        
        // Add to overall totals
        totalRevenue += timeFrameData.totalRevenue;
        totalAppointments += timeFrameData.appointmentCount;
      }

      data.push(timeFrameData);
    }

    // Calculate overall average
    const overallAverageRevenue = totalAppointments > 0 
      ? totalRevenue / totalAppointments 
      : 0;

    return res.status(200).json({ 
      message: 'Average revenue data retrieved successfully.',
      serviceId: service._id,
      serviceName: service.name,
      overallAverageRevenue,
      data
    });

  } catch (error) {
    console.error('Error getting average revenue:', error);
    return res.status(500).json({ error: 'Internal server error.' });
  }
};

/**
 * Get the service with the highest average revenue within a specified time interval
 */
export const getHighestAverageRevenueService = async (req: any, res: Response, next: NextFunction): Promise<Response> => {
  try {
    const userId = req.user?._id;
    const { timeInterval } = req.query;

    // Get all services for this user
    const userServices = await services.find({ userId }).exec();
    if (!userServices.length) {
      return res.status(404).json({ error: 'No services found for this user.' });
    }

    const serviceIds = userServices.map(service => service._id);
    const timeFrames = generateTimeFrames(timeInterval || 'today');

    // Combine all time frames for the overall calculation
    const startDate = timeFrames[0].start;
    const endDate = timeFrames[timeFrames.length - 1].end;

    // Aggregate to find average revenue per service
    const servicesRevenue = await clientAppointment.aggregate([
      {
        $match: {
          userId: userId,
          service: { $in: serviceIds },
          status: 'completed',
          date: { $gte: startDate, $lte: endDate }
        }
      },
      {
        $group: {
          _id: '$service',
          totalRevenue: { $sum: '$revenue' },
          appointmentCount: { $sum: 1 }
        }
      },
      {
        $project: {
          service: '$_id',
          totalRevenue: 1,
          appointmentCount: 1,
          averageRevenue: { $divide: ['$totalRevenue', '$appointmentCount'] }
        }
      },
      {
        $sort: { averageRevenue: -1 }
      }
    ]);

    if (!servicesRevenue.length) {
      return res.status(404).json({ 
        message: 'No completed appointments found for the specified time interval.' 
      });
    }

    // Get the service with highest average revenue
    const highestAverageService = servicesRevenue[0];
    const serviceDetails = userServices.find(service => (service._id as unknown as string).toString() === highestAverageService.service.toString());
    // Get time frame breakdown for the highest average service
    const timeFrameData = [];
    for (const timeFrame of timeFrames) {
      const timeFrameResult = await clientAppointment.aggregate([
        {
          $match: {
            userId: userId,
            service: highestAverageService.service,
            status: 'completed',
            date: { $gte: timeFrame.start, $lte: timeFrame.end }
          }
        },
        {
          $group: {
            _id: null,
            totalRevenue: { $sum: '$revenue' },
            appointmentCount: { $sum: 1 }
          }
        }
      ]);

      timeFrameData.push({
        timeFrame: timeFrame.label,
        totalRevenue: timeFrameResult.length > 0 ? timeFrameResult[0].totalRevenue : 0,
        appointmentCount: timeFrameResult.length > 0 ? timeFrameResult[0].appointmentCount : 0,
        averageRevenue: timeFrameResult.length > 0 && timeFrameResult[0].appointmentCount > 0 
          ? timeFrameResult[0].totalRevenue / timeFrameResult[0].appointmentCount 
          : 0
      });
    }

    return res.status(200).json({
      message: 'Service with highest average revenue retrieved successfully.',
      service: {
        id: serviceDetails?._id,
        name: serviceDetails?.name,
        totalRevenue: highestAverageService.totalRevenue,
        appointmentCount: highestAverageService.appointmentCount,
        averageRevenue: highestAverageService.averageRevenue
      },
      timeFrameData
    });

  } catch (error) {
    console.error('Error getting highest average revenue service:', error);
    return res.status(500).json({ error: 'Internal server error.' });
  }
};

/**
 * Get the service with the highest total revenue within a specified time interval
 */
export const getHighestTotalRevenueService = async (req: any, res: Response, next: NextFunction): Promise<Response> => {
  try {
    const userId = req.user?._id;
    const { timeInterval } = req.query;

    // Get all services for this user
    const userServices = await services.find({ userId }).exec();
    if (!userServices.length) {
      return res.status(404).json({ error: 'No services found for this user.' });
    }

    const serviceIds = userServices.map(service => service._id);
    const timeFrames = generateTimeFrames(timeInterval || 'today');

    // Combine all time frames for the overall calculation
    const startDate = timeFrames[0].start;
    const endDate = timeFrames[timeFrames.length - 1].end;

    // Aggregate to find total revenue per service
    const servicesRevenue = await clientAppointment.aggregate([
      {
        $match: {
          userId: userId,
          service: { $in: serviceIds },
          status: 'completed',
          date: { $gte: startDate, $lte: endDate }
        }
      },
      {
        $group: {
          _id: '$service',
          totalRevenue: { $sum: '$revenue' },
          appointmentCount: { $sum: 1 }
        }
      },
      {
        $sort: { totalRevenue: -1 }
      }
    ]);

    if (!servicesRevenue.length) {
      return res.status(404).json({ 
        message: 'No completed appointments found for the specified time interval.' 
      });
    }

    // Get the service with highest total revenue
    const highestRevenueService = servicesRevenue[0];
    const serviceDetails = userServices.find(s => (s._id as unknown as string).toString() === highestRevenueService._id.toString());

    // Get time frame breakdown for the highest revenue service
    const timeFrameData = [];
    for (const timeFrame of timeFrames) {
      const timeFrameResult = await clientAppointment.aggregate([
        {
          $match: {
            userId: userId,
            service: highestRevenueService._id,
            status: 'completed',
            date: { $gte: timeFrame.start, $lte: timeFrame.end }
          }
        },
        {
          $group: {
            _id: null,
            totalRevenue: { $sum: '$revenue' },
            appointmentCount: { $sum: 1 }
          }
        }
      ]);

      timeFrameData.push({
        timeFrame: timeFrame.label,
        totalRevenue: timeFrameResult.length > 0 ? timeFrameResult[0].totalRevenue : 0,
        appointmentCount: timeFrameResult.length > 0 ? timeFrameResult[0].appointmentCount : 0,
        averageRevenue: timeFrameResult.length > 0 && timeFrameResult[0].appointmentCount > 0 
          ? timeFrameResult[0].totalRevenue / timeFrameResult[0].appointmentCount 
          : 0
      });
    }

    return res.status(200).json({
      message: 'Service with highest total revenue retrieved successfully.',
      service: {
        id: serviceDetails?._id,
        name: serviceDetails?.name,
        totalRevenue: highestRevenueService.totalRevenue,
        appointmentCount: highestRevenueService.appointmentCount,
        averageRevenue: highestRevenueService.appointmentCount > 0 
          ? highestRevenueService.totalRevenue / highestRevenueService.appointmentCount 
          : 0
      },
      timeFrameData
    });

  } catch (error) {
    console.error('Error getting highest total revenue service:', error);
    return res.status(500).json({ error: 'Internal server error.' });
  }
};


export const getServiceReturnRateMonthly = async (req: any, res: Response, next: NextFunction): Promise<Response> => {
  try {
    const userId = req.user?._id;
    const { serviceId } = req.params;
    const service = await services.findOne({ _id: serviceId, userId }).select('name');

    if (!service) {
      return res.status(404).json({ error: 'Service not found.' });
    }

    const currentYear = new Date().getFullYear();
    const monthNames = [
      'January', 'February', 'March', 'April', 'May', 'June',
      'July', 'August', 'September', 'October', 'November', 'December'
    ];

    const serviceAppointments = await clientAppointment.find({
      service: serviceId,
      date: { $gte: new Date(`${currentYear}-01-01`), $lte: new Date(`${currentYear}-12-31`) }
    });

    const totalClients = await businessClient.countDocuments({ userId });
    const monthlyReturnRates: any = {};

    serviceAppointments.forEach((appointment) => {
      const monthIndex = new Date(appointment.date).getMonth();
      const monthName = monthNames[monthIndex];
      const clientId: string = appointment.clientId.toString();
      
      if (!monthlyReturnRates[monthName]) {
        monthlyReturnRates[monthName] = { totalClients, totalReturningClients: 0, clientCounts: {} };
      }

      if (!monthlyReturnRates[monthName].clientCounts[clientId]) {
        monthlyReturnRates[monthName].clientCounts[clientId] = 0;
      }

      monthlyReturnRates[monthName].clientCounts[clientId]++;
    });

    Object.keys(monthlyReturnRates).forEach((month) => {
      const monthData = monthlyReturnRates[month];
      monthData.totalReturningClients = Object.values(monthData.clientCounts).filter((count: any) => count > 1).length;
      monthData.returnRate = totalClients > 0 ? ((monthData.totalReturningClients / totalClients) * 100).toFixed(2) + '%' : '0.00%';
      delete monthData.clientCounts;
    });

    return res.status(200).json({
      serviceId: service._id,
      serviceName: service.name,
      monthlyReturnRates: Object.entries(monthlyReturnRates).map(([month, data]: any) => ({
        month,
        totalClients: data.totalClients,
        totalReturningClients: data.totalReturningClients,
        returnRate: data.returnRate
      }))
    });
  } catch (error) {
    console.error('Error getting service return rate monthly:', error);
    return res.status(500).json({ error: 'Internal server error.' });
  }
};
export const getServiceReturnRateQuarterly = async (req: any, res: Response, next: NextFunction): Promise<Response> => {
  try {
    const userId = req.user?._id;
    const { serviceId } = req.params;
    const currentYear = new Date().getFullYear();

    const service = await services.findOne({ _id: serviceId, userId }).select('name');
    if (!service) {
      return res.status(404).json({ error: 'Service not found.' });
    }

    const serviceAppointments = await clientAppointment.find({
      service: serviceId,
      date: { $gte: new Date(`${currentYear}-01-01`), $lte: new Date(`${currentYear}-12-31`) }
    });

    const totalClients = await businessClient.countDocuments({ userId });
    const quarterlyReturnRates: any = {
      Q1: { totalClients: 0, totalReturningClients: 0 },
      Q2: { totalClients: 0, totalReturningClients: 0 },
      Q3: { totalClients: 0, totalReturningClients: 0 },
      Q4: { totalClients: 0, totalReturningClients: 0 }
    };

    serviceAppointments.forEach((appointment) => {
      const month = new Date(appointment.date).getMonth() + 1;
      let quarter: string;

      if (month <= 3) quarter = 'Q1';
      else if (month <= 6) quarter = 'Q2';
      else if (month <= 9) quarter = 'Q3';
      else quarter = 'Q4';

      const clientId: string = appointment.clientId.toString();

      if (!quarterlyReturnRates[quarter][clientId]) {
        quarterlyReturnRates[quarter][clientId] = 0;
      }

      quarterlyReturnRates[quarter][clientId]++;
    });

    Object.keys(quarterlyReturnRates).forEach((quarter) => {
      const quarterData = quarterlyReturnRates[quarter];
      quarterData.totalReturningClients = Object.values(quarterData).filter((count: any) => count > 1).length;
      quarterData.returnRate = totalClients > 0 ? ((quarterData.totalReturningClients / totalClients) * 100).toFixed(2) + '%' : '0.00%';
    });

    return res.status(200).json({
      serviceId: service._id,
      serviceName: service.name,
      quarterlyReturnRates: Object.entries(quarterlyReturnRates).map(([quarter, data]: any) => ({
        quarter,
        totalClients: data.totalClients,
        totalReturningClients: data.totalReturningClients,
        returnRate: data.returnRate
      }))
    });
  } catch (error) {
    console.error('Error getting return rate quarterly:', error);
    return res.status(500).json({ error: 'Internal server error.' });
  }
};

//creating controllers to get Generate a list of clients who haven’t booked a service recently, based on the average return interval.

/**
 * Get clients who haven't booked a specific service recently based on average return interval
 */
export const getClientsForReEngagement = async (req: any, res: Response, next: NextFunction): Promise<Response> => {
  try {
    const userId = req.user._id;
    const { serviceId } = req.params;
    const { dayThreshold = 30 } = req.query;
    
    const service = await services.findOne({ _id: serviceId, userId });
    if (!service) {
      return res.status(404).json({ error: 'Service not found or does not belong to this user.' });
    }

    const allClients = await businessClient.find({ userId });
    if (!allClients.length) {
      return res.status(404).json({ error: 'No clients found for this user.' });
    }

    // Calculate the average return interval for this service
    const clientAppointments = await clientAppointment.aggregate([
      {
        $match: {
          userId: new mongoose.Types.ObjectId(userId),
          service: new mongoose.Types.ObjectId(serviceId),
          status: 'completed'
        }
      },
      {
        $sort: { date: 1 }
      },
      {
        $group: {
          _id: '$clientId',
          appointments: { $push: { date: '$date' } },
          lastAppointmentDate: { $last: '$date' },
          appointmentCount: { $sum: 1 }
        }
      }
    ]);

    // Calculate average return interval in days
    let totalIntervalDays = 0;
    let intervalCount = 0;

    clientAppointments.forEach(client => {
      if (client.appointmentCount > 1) {
        const appointments = client.appointments;
        for (let i = 1; i < appointments.length; i++) {
          const currentDate = new Date(appointments[i].date);
          const previousDate = new Date(appointments[i-1].date);
          const intervalDays = Math.floor((currentDate.getTime() - previousDate.getTime()) / (1000 * 60 * 60 * 24));
          totalIntervalDays += intervalDays;
          intervalCount++;
        }
      }
    });

    // Default to the provided threshold if we can't calculate an average
    const averageReturnIntervalDays = intervalCount > 0 
      ? Math.floor(totalIntervalDays / intervalCount)
      : Number(dayThreshold);

    // Current date for comparison
    const currentDate = new Date();
    
    // Find clients who haven't booked this specific service in longer than the average return interval
    const clientsForReEngagement = [];
    
    for (const client of allClients) {
      const clientServiceAppointment = clientAppointments.find(
        ca => ca._id.toString() === (client as { _id: mongoose.Types.ObjectId })._id.toString()
      );
      
      if (!clientServiceAppointment) {
        // Client has booked other services but not this specific one
        clientsForReEngagement.push({
          client: {
            _id: client._id,
            name: client.clientName,
            email: client.clientEmail,
            phone: client.phoneNumber
          },
          lastAppointment: null,
          daysSinceLastAppointment: null,
          averageReturnInterval: averageReturnIntervalDays,
          status: 'Not booked this service',
          potentialValue: 'High' // Potential new service adoption
        });
      } else {
        const lastAppointmentDate = new Date(clientServiceAppointment.lastAppointmentDate);
        const daysSinceLastAppointment = Math.floor(
          (currentDate.getTime() - lastAppointmentDate.getTime()) / (1000 * 60 * 60 * 24)
        );
        
        if (daysSinceLastAppointment > averageReturnIntervalDays) {
          clientsForReEngagement.push({
            client: {
              _id: client._id,
              name: client.clientName,
              email: client.clientEmail,
              phone: client.phoneNumber
            },
            lastAppointment: lastAppointmentDate,
            daysSinceLastAppointment,
            averageReturnInterval: averageReturnIntervalDays,
            status: 'Overdue',
            daysOverdue: daysSinceLastAppointment - averageReturnIntervalDays,
            potentialValue: 'Medium' // Existing customer for this service
          });
        }
      }
    }

    // Sort by most overdue first, then by those who haven't tried this service
    clientsForReEngagement.sort((a, b) => {
      if (a.status === 'Not booked this service' && b.status === 'Not booked this service') return 0;
      if (a.status === 'Not booked this service') return 1;
      if (b.status === 'Not booked this service') return -1;
      return (b.daysOverdue ?? 0) - (a.daysOverdue ?? 0);
    });

    return res.status(200).json({
      message: 'Clients for re-engagement retrieved successfully',
      service: {
        id: service._id,
        name: service.name
      },
      averageReturnIntervalDays,
      totalClientsForReEngagement: clientsForReEngagement.length,
      clientsForReEngagement
    });

  } catch (error) {
    console.error('Error getting clients for re-engagement:', error);
    return res.status(500).json({ error: 'Internal server error.' });
  }
};



/**
 * Get clients who haven't booked any service recently
 */
export const getInactiveClients = async (req: any, res: Response, next: NextFunction): Promise<Response> => {
  try {
    const userId = req.user._id;
    const { inactiveDays = 60 } = req.query; // Default threshold is 60 days
    
    // Get all clients for this user
    const allClients = await businessClient.find({ userId });
    if (!allClients.length) {
      return res.status(404).json({ error: 'No clients found for this user.' });
    }

    // Calculate the date threshold
    const currentDate = new Date();
    const thresholdDate = new Date();
    thresholdDate.setDate(currentDate.getDate() - Number(inactiveDays));
    
    // Find the last appointment date for each client
    const clientLastAppointments = await clientAppointment.aggregate([
      {
        $match: {
          userId: new mongoose.Types.ObjectId(userId)
        }
      },
      {
        $sort: { date: -1 }
      },
      {
        $group: {
          _id: '$clientId',
          lastAppointmentDate: { $first: '$date' },
          totalAppointments: { $sum: 1 }
        }
      }
    ]);

    // Identify inactive clients
    const inactiveClients = [];
    
    for (const client of allClients) {
      const clientAppointment = clientLastAppointments.find(
        ca => ca._id.toString() === (client as { _id: mongoose.Types.ObjectId })._id.toString()
      );
      
      // All clients should have at least one appointment since they're created when booking
      if (clientAppointment) {
        const lastAppointmentDate = new Date(clientAppointment.lastAppointmentDate);
        
        if (lastAppointmentDate < thresholdDate) {
          const daysSinceLastAppointment = Math.floor(
            (currentDate.getTime() - lastAppointmentDate.getTime()) / (1000 * 60 * 60 * 24)
          );
          
          inactiveClients.push({
            client: {
              _id: client._id,
              name: client.clientName,
              email: client.clientEmail,
              phone: client.phoneNumber
            },
            lastAppointment: lastAppointmentDate,
            daysSinceLastAppointment,
            totalAppointments: clientAppointment.totalAppointments,
            status: 'Inactive',
            inactivityLevel: daysSinceLastAppointment > inactiveDays * 2 ? 'High' : 'Medium'
          });
        }
      }
    }

    // Sort by most inactive first (days since last appointment)
    inactiveClients.sort((a, b) => b.daysSinceLastAppointment - a.daysSinceLastAppointment);

    return res.status(200).json({
      message: 'Inactive clients retrieved successfully',
      inactiveDaysThreshold: Number(inactiveDays),
      totalInactiveClients: inactiveClients.length,
      inactiveClients
    });

  } catch (error) {
    console.error('Error getting inactive clients:', error);
    return res.status(500).json({ error: 'Internal server error.' });
  }
};

/**
 * Get suggested re-engagement strategies for clients
 * This provides personalized recommendations based on client history
 */
export const getReEngagementStrategies = async (req: any, res: Response, next: NextFunction): Promise<Response> => {
  try {
    const userId = req.user._id;
    const { clientId } = req.params;
    
    // Verify client exists and belongs to user
    const client = await businessClient.findOne({ _id: clientId, userId });
    if (!client) {
      return res.status(404).json({ error: 'Client not found or does not belong to this user.' });
    }

    // Get client's appointment history
    const appointmentHistory = await clientAppointment.find({ 
      userId, 
      clientId 
    }).sort({ date: -1 }).populate('service', 'name price');

    // Get client's favorite services (most booked)
    const favoriteServices = await clientAppointment.aggregate([
      {
        $match: {
          userId: new mongoose.Types.ObjectId(userId),
          clientId: new mongoose.Types.ObjectId(clientId)
        }
      },
      {
        $group: {
          _id: '$service',
          count: { $sum: 1 },
          totalRevenue: { $sum: '$revenue' }
        }
      },
      {
        $sort: { count: -1 }
      },
      {
        $limit: 3
      }
    ]);

    // Populate service details for favorite services
    const favoriteServicesDetails = [];
    for (const service of favoriteServices) {
      const serviceDetails = await services.findById(service._id);
      if (serviceDetails) {
        favoriteServicesDetails.push({
          service: {
            _id: serviceDetails._id,
            name: serviceDetails.name
          },
          bookingCount: service.count,
          totalRevenue: service.totalRevenue
        });
      }
    }

    // Get services the client hasn't tried yet
    const allServices = await services.find({ userId });
    const bookedServiceIds = favoriteServices.map(s => s._id.toString());
    const unbookedServices = allServices
      .filter((service) => !bookedServiceIds.includes((service._id as mongoose.Types.ObjectId).toString()))
      .map(service => ({
        _id: service._id,
        name: service.name
      }));

    // Determine client status and engagement strategy
    let clientStatus = 'Active';
    let engagementStrategy = [];
    
    // Since all clients have at least one appointment
    const lastAppointmentDate = new Date(appointmentHistory[0].date);
    const currentDate = new Date();
    const daysSinceLastAppointment = Math.floor(
      (currentDate.getTime() - lastAppointmentDate.getTime()) / (1000 * 60 * 60 * 24)
    );
    
    if (daysSinceLastAppointment > 90) {
      clientStatus = 'Long-term inactive';
      engagementStrategy = [
        'Send a "We miss you" message with a special discount',
        'Highlight new services or improvements since their last visit',
        'Offer a loyalty reward for returning'
      ];
    } else if (daysSinceLastAppointment > 45) {
      clientStatus = 'Recently inactive';
      engagementStrategy = [
        'Send a gentle reminder about booking their next appointment',
        'Offer a small discount on their favorite service',
        'Share testimonials or results from similar clients'
      ];
    } else {
      clientStatus = 'Active';
      engagementStrategy = [
        'Send a thank you message for their recent visit',
        'Recommend complementary services to their recent bookings',
        'Introduce a referral program for friends and family'
      ];
    }

    return res.status(200).json({
      message: 'Re-engagement strategies retrieved successfully',
      client: {
        _id: client._id,
        name: client.clientName,
        email: client.clientEmail,
        phone: client.phoneNumber,
        totalAppointments: client.totalAppointments
      },
      clientStatus,
      lastAppointment: appointmentHistory[0].date,
      daysSinceLastAppointment,
      favoriteServices: favoriteServicesDetails,
      unbookedServices: unbookedServices.length > 0 ? unbookedServices : [],
      recommendedStrategies: engagementStrategy,
      appointmentHistory: appointmentHistory.map(apt => ({
        _id: apt._id,
        date: apt.date,
        service: apt.service,
        revenue: apt.revenue,
        status: apt.status
      }))
    });

  } catch (error) {
    console.error('Error getting re-engagement strategies:', error);
    return res.status(500).json({ error: 'Internal server error.' });
  }
};

