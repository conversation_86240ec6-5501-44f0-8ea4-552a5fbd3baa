import dotenv from 'dotenv';
import Stripe from 'stripe';
dotenv.config();
if(!process.env.STRIPE_KEY){
  throw new Error('Missing necessary stripes environment variables.');
}
export const stripeClient = new Stripe(process.env.STRIPE_KEY!);
console.log(process.env.STRIPE_KEY)

export const  createCustomer = async(name:string, email:string)=>{
    try{
        const customer = await stripeClient.customers.create({
            name: name,
            email:email,
            });
          return customer
    }catch(error){
        console.log('error-create-customer', error)
        throw error
    }
    
}

export const RegisterStripeAccount = async(stripeUserAccountId:string,userId:string)=>{
  try{
      const Account = await stripeClient.accounts.update(stripeUserAccountId,{
        metadata: {
          userId:userId,
        },
      })
      return Account
  }catch(error){
      console.log('error-register-customer', error)
      throw error
  }
  
}

export const createStripeProduct =  async(name:string, description:string)=>{
    try{
    const product = await stripeClient.products.create({
        name:name,
        description:description
    });
    return product
}catch(error){
    console.log('error-create-product', error)
    throw error
}
}

export const createStripePrice =  async(productId:string, currency:string, amount:number, recurring: "day" | "week" | "month" | "year")=>{
    try{
    const price = await stripeClient.prices.create({
        currency,
        unit_amount:amount,
        recurring: {
          interval: recurring,
        },
        product:productId
      });
    return price
}catch(error){
    console.log('error-create-price', error)
    throw error
}
}
export const CreateStripeSubscription =async (customerId: string, priceId: string)=>{
    try {
        const subscription = await stripeClient.subscriptions.create({
          customer: customerId,
          items: [{ price: priceId }],
          payment_behavior: 'default_incomplete', 
          expand: ['latest_invoice.payment_intent'], 
          payment_settings: {
            save_default_payment_method: 'on_subscription'
          }
        });
        return subscription;
      } catch (error) {
        console.error('Error creating subscription:', error);
        throw error;
      }
}

export const UpdateStripeSubscription =async (stripeSubscriptionId: string, stripePriceId: string)=>{
  try {
      const updatedSubscription = await stripeClient.subscriptions.update(
        stripeSubscriptionId,
        {
          cancel_at_period_end: false, 
          proration_behavior: 'create_prorations',
          items: [{ price:stripePriceId }],
        }
      );
      return updatedSubscription;
    } catch (error) {
      console.error('Error creating subscription:', error);
      throw error;
    }
}

// export const handleSubPaymentIntent =async (subscription:Stripe.Subscription)=>{
//   try {
//     const paymentIntent = subscription.latest_invoice?.payment_intent;
//     if (paymentIntent?.status === 'requires_payment_method') {
//       // If the payment method is required, send the client the PaymentIntent client secret
//       // to complete the payment on the frontend.
//       return { clientSecret: paymentIntent.client_secret };
//     } else if (paymentIntent?.status === 'succeeded') {
//       console.log('Subscription and payment succeeded');
//       return { subscription };
//     }
//     } catch (error) {
//       console.error('Error creating subscription:', error);
//       throw error;
//     }
// }


export const createSetupIntent =async (customerId: string)=>{
  try {
    const setupIntent = await stripeClient.setupIntents.create({
      customer: customerId,
      payment_method_types: ['card'],
    });
    return setupIntent.client_secret 
  } catch (error) {
    console.error('Error creating setup intent:', error);
    throw error
  }
}

export const attachPaymentDefaultMethodtoCustomer =async (customerId: string,paymentMethodId:string)=>{
  try {
    const paymentMethod = await stripeClient.paymentMethods.attach(paymentMethodId, {
      customer: customerId,
    });
    // Set it as the default payment method for the customer
    await stripeClient.customers.update(customerId, {
      invoice_settings: { default_payment_method: paymentMethodId },
    });
    
    return paymentMethod
  } catch (error:any) {
    if (
      error.type === 'StripeInvalidRequestError' &&
      error.message.includes('The payment method you provided has already been attached to a customer')
    ) {
      console.log('Payment method is already attached to the customer.');
      const existingPaymentMethod = await stripeClient.paymentMethods.retrieve(paymentMethodId);
      return existingPaymentMethod;
    }

    console.error('Error attaching default payment method:', error);
    throw error; 
  }

}
export const attachPaymentMethodtoCustomer =async (customerId: string,paymentMethodId:string)=>{
  try {

    const paymentMethod = await stripeClient.paymentMethods.attach(paymentMethodId, {
      customer: customerId,
    });
    return paymentMethod
  } catch (error:any) {
    if (
      error.type === 'StripeInvalidRequestError' &&
      error.message.includes('The payment method you provided has already been attached to a customer')
    ) {
      console.log('Payment method is already attached to the customer.');
      const existingPaymentMethod = await stripeClient.paymentMethods.retrieve(paymentMethodId);
      return existingPaymentMethod;
    }

    console.error('Error attaching default payment method:', error);
    throw error; 
  }

}

export const retrieveSubscription =async (stripeSubscriptionId: string)=>{
  try {

    const subscription = await stripeClient.subscriptions.retrieve(stripeSubscriptionId);
    return subscription
  } catch (error) {
    console.error('Error getting subscription:', error);
    if (error instanceof Error && 'statusCode' in error && error.statusCode === 404) {
      throw new Error('Stripe Subscription not found');
    }else{
      throw error
    }
  }

}

export const retrieveInvoice =async (latestInvoiceId: string)=>{
  try {

    const invoice = await stripeClient.invoices.retrieve(latestInvoiceId);
    return invoice
  } catch (error) {
    console.error('Error getting invoice:', error);
    throw error
  }
}

export const retrievePaymentIntentofInvoice =async (paymentIntentId: string)=>{
  try {
    const paymentIntent = await stripeClient.paymentIntents.retrieve(paymentIntentId);
    return paymentIntent
  } catch (error) {
    console.error('Error getting paymentIntent:', error);
    throw error
  }
}
export const createStripePaymentIntentWithMethod = async (customerId:string,amount:number, currency:string, paymentMethodId:string, metadata:{},receiptEmail:string)=>{
  try{
    const paymentIntent = await stripeClient.paymentIntents.create({
      amount,
      currency:currency ? currency: 'usd',
      payment_method: paymentMethodId,
      customer: customerId,
      setup_future_usage: 'off_session',
      confirm: false, // Let the frontend handle the confirmation
      metadata:metadata,
      receipt_email:receiptEmail
    });
    return paymentIntent
  }catch (error){
    console.error('Error getting paymentIntent:', error);
    throw error
  }
}
export const createStripePaymentIntentForService  = async (
  amount:number, currency:string,metadata:{},receiptEmail:string,
  applicationFee:number,ownerStripeAccountId:string
)=>{
  try{
    const paymentIntent = await stripeClient.paymentIntents.create({
      amount,
      currency:currency ? currency: 'usd',
      confirm: false,
      metadata,
      receipt_email:receiptEmail,
      application_fee_amount:applicationFee,
      transfer_data:{
        destination:ownerStripeAccountId
      }
    });
    return paymentIntent
  }catch (error){
    console.error('Error getting paymentIntent:', error);
    throw error
  }
}





