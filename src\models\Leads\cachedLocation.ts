import mongoose, { Schema, Document } from 'mongoose';

interface ICachedLocation extends Document {
  location: string;
  longitude: number;
  latitude: number;
}

const CachedLocationSchema: Schema = new Schema({
  location: {
    type: String,
    required: true,
    lowercase: true,
  },
  longitude: {
    type: Number,
    required: true,
  },
  latitude: {
    type: Number,
    required: true,
  },
});

export default  mongoose.model<ICachedLocation>('CachedLocation', CachedLocationSchema);

