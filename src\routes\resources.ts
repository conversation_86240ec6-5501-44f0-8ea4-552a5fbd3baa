import { Router } from 'express';
import { authMiddleware, authorizeRoles } from '../middleware/authService';
import { sendUserFeedback } from '../controllers/resources';
const router: Router = Router();

router.post('/submit-feedback', sendUserFeedback);
router.post('/block-ip', authMiddleware,authorizeRoles(['admin']), sendUserFeedback);
router.delete('/unblock-ip', authMiddleware, authorizeRoles(['admin']), sendUserFeedback);

export default router;