import mongoose, { Schema, Document } from 'mongoose';

export interface ICategory extends Document {
    userId: mongoose.Types.ObjectId; 
    name: string;
}

const categorySchema: Schema = new Schema({
    userId: { type: Schema.Types.ObjectId, ref: 'User', required: true },
    name: { type: String, required: true},
}, { timestamps: true });
// categorySchema.index({ userId: 1, name: 1 }, { unique: true });

export default mongoose.model<ICategory>('Category', categorySchema);
