import {  Response, NextFunction } from 'express';
import calendarSettings, { ICalendarSettings } from '../models/calendarSettings';
import blockTime, { IBlockedTime } from '../models/blockTime';
import moment from 'moment-timezone';

const isWithinWorkingHours = (startTime: string, endTime: string, dayStartTime: string, dayClosingTime: string) => {
    return startTime >= dayStartTime && endTime <= dayClosingTime;
};

// Create Blocked Time
export const createBlockedTime = async (req: any, res: Response, next: NextFunction): Promise<Response> => {
    const userId = req.user?._id;
    const { calendarId } = req.params;
    const { startTime, endTime, startDate, endDate, notes }: IBlockedTime = req.body;

    try {
        const calendarSet = await calendarSettings.findOne({ userId });
        if (!calendarSet) {
            return res.status(404).json({ error: 'Calendar settings not found for user' });
        }

        const requestedDay = moment(startDate).format('dddd').toLowerCase();
        if (!calendarSet.workdays.some(workday => workday.day === requestedDay)) {
            return res.status(400).json({ error: `The selected date (${requestedDay}) is not a working day for the user.` });
        }

        const daySettings = calendarSet.workdays.find(workday => workday.day === requestedDay);
        if (!daySettings) {
            return res.status(400).json({ error: `No working hours defined for ${requestedDay}` });
        }

        const { startTime: calendarStartTime, closingTime: calendarClosingTime } = daySettings;

        if (!isWithinWorkingHours(startTime, endTime, calendarStartTime, calendarClosingTime)) {
            return res.status(400).json({ error: `Blocked time must be within working hours (${calendarStartTime} - ${calendarClosingTime})` });
        }

        // Create a new blocked time entry
        const blockedTime = new blockTime({
            userId,
            calendarId,
            startTime,
            endTime,
            startDate,
            endDate,
            notes,
        });

        await blockedTime.save();

        return res.status(201).json({ success: true, message: 'Blocked time created successfully', blockedTime });
    } catch (error) {
        console.error('Error creating blocked time:', error);
        next(error);
        return res.status(500).json({
            success: false,
            error: 'Internal server error',
        });
    }
};


export const getBlockedTimesByUser = async (req: any, res: Response, next: NextFunction): Promise<Response> =>{
  const userId = req.user?._id
  try {
      const blockedTimes = await blockTime.find({userId});

      if (!blockedTimes) {
          return res.status(404).json({ error: 'No blocked times found for this user' });
      }
      return res.status(200).json({
        sucess:true,
        blockedTimes
      });
  } catch (error) {
      console.error('Error fetching blocked times:', error);
      next(error);
        return res.status(500).json({
            success: false,
            error: 'Internal server error',
        });
  }
};

// Get all Blocked Time for a calendar
export const getBlockedTimesByCalendar = async (req: any, res: Response, next: NextFunction): Promise<Response> =>{
    const userId = req.user?._id
    const { calendarId } = req.params;
  try {
      const blockedTimes = await blockTime.find({ calendarId,userId});

      if (!blockedTimes) {
          return res.status(404).json({ error: 'No blocked times found for this calendar' });
      }
      return res.status(200).json({
        success:true,
        blockedTimes});
  } catch (error) {
    console.error('Error fetching blocked time:', error);
      next(error);
        return res.status(500).json({
            success: false,
            error: 'Internal server error',
        });
  }
};

export const updateBlockedTime = async (req: any, res: Response, next: NextFunction): Promise<Response> => {
    const userId = req.user?._id;
    const { blockedTimeId } = req.params;
    const { startTime, endTime, startDate, endDate, notes }: IBlockedTime = req.body;

    try {
        // Find the blocked time entry for the user
        const blockedTime = await blockTime.findOne({ _id: blockedTimeId, userId });
        if (!blockedTime) {
            return res.status(404).json({ error: 'Blocked time entry not found' });
        }

        // Fetch calendar settings for validation
        const calendarSet = await calendarSettings.findOne({ userId });
        if (!calendarSet) {
            return res.status(404).json({ error: 'Calendar settings not found for user' });
        }

        // Validate if the new blocked time is within working hours
        const { workdays, timezone } = calendarSet;
        const requestedDay = moment(startDate).format('dddd').toLowerCase();

        if (!workdays.some(workday => workday.day === requestedDay)) {
            return res.status(400).json({ error: `Blocked time is not allowed on ${requestedDay}` });
        }

        const daySettings = workdays.find(workday => workday.day === requestedDay);
        if (!daySettings) {
            return res.status(400).json({ error: `No working hours defined for ${requestedDay}` });
        }

        const { startTime: calendarStartTime, closingTime: calendarClosingTime } = daySettings;

        if (!isWithinWorkingHours(startTime, endTime, calendarStartTime, calendarClosingTime)) {
            return res.status(400).json({ error: `Blocked time must be within working hours (${calendarStartTime} - ${calendarClosingTime})` });
        }

        // Update the blocked time entry
        blockedTime.startTime = startTime;
        blockedTime.endTime = endTime;
        blockedTime.startDate = startDate;
        blockedTime.endDate = endDate;
        blockedTime.notes = notes;

        await blockedTime.save();

        return res.status(200).json({ success: true, message: 'Blocked time updated successfully', blockedTime });
    } catch (error) {
        console.error('Error updating blocked time:', error);
        next(error);
        return res.status(500).json({
            success: false,
            error: 'Internal server error',
        });
    }
};


// Delete Blocked Time
export const deleteBlockedTime = async (req: any, res: Response, next: NextFunction): Promise<Response> =>{
  const { blockedTimeId } = req.params;
  try {
      const blockedTime = await blockTime.findByIdAndDelete(blockedTimeId);

      if (!blockedTime) {
          return res.status(404).json({ error: 'Blocked time entry not found' });
      }

      return res.status(200).json({ message: 'Blocked time deleted successfully' });
  } catch (error) {
      console.error('Error deleting blocked time:', error);
      next(error);
        return res.status(500).json({
            success: false,
            error: 'Internal server error',
        });
  }
};






export const getCalendarSettings = async (req:any, res: Response, next: NextFunction): Promise<Response> => {
    const userId = req.user?._id
    try {
        const settings = await calendarSettings.findOne({ userId });
        if (!settings) {
            return res.status(404).json({
                success: false,
                message: 'Calendar settings not found for the user',
            });
        }

        return res.status(200).json({
            success: true,
            calendarSettings: settings,
        });

    } catch (error) {
        next(error);
        return res.status(500).json({
            success: false,
            error: 'Internal server error',
        });
    }
};

export const updateCalendarSettings = async (req: any, res: Response, next: NextFunction): Promise<Response> => {
    const userId = req.user?._id;
    const {
        workdays, // Array of workday settings, each with day, startTime, closingTime
        timeslots,
        customTimeslotDuration,
        timezone,
        advanceBookingLimit,
    }= req.body;
    // console.log(workdays)
    // console.log(timeslots)
    // console.log(timezone)
    console.log(advanceBookingLimit)
    try {
        const settings = await calendarSettings.findOne({ userId });
        if (!settings) {
            return res.status(404).json({
                success: false,
                error: 'Calendar settings not found for this user',
            });
        }

        if (workdays && Array.isArray(workdays)) {
            settings.workdays = workdays.map(daySettings => {
                const { day, startTime, closingTime } = daySettings;
                if (!day || !startTime || !closingTime) {
                    throw new Error('Invalid workday settings. Each day must have a startTime and closingTime.');
                }
                return { day, startTime, closingTime };
            });
        }

        if (timezone) {
            settings.timezone = timezone;
        }
        if (advanceBookingLimit) {
            settings.advanceBookingLimit = advanceBookingLimit;
        }

        if (timeslots) {
            settings.timeslots = timeslots;
            if (timeslots === 'custom' && customTimeslotDuration) {
                settings.customTimeslotDuration = customTimeslotDuration;
            } else if (timeslots !== 'custom') {
                settings.customTimeslotDuration = undefined;
            }
        }

        const updatedSettings = await settings.save();

        return res.status(200).json({
            success: true,
            message: 'Calendar settings updated successfully',
            calendarSettings: updatedSettings,
        });

    } catch (error) {
        console.error('Error updating calendar settings:', error);
        next(error);
        return res.status(500).json({
            success: false,
            error: 'Internal Server Error',
        });
    }
};



export const getAvailableWorkingDays = async (req: any, res: Response, next: NextFunction): Promise<Response> => {
    try {
      const { userId } = req.params;
  
      // Find the calendar settings for the user
      const CalendarSet = await calendarSettings.findOne({ userId });
      if (!CalendarSet) {
        return res.status(404).json({ error: 'Calendar settings not found' });
      }
        // Extract the workdays and return only the days as strings
        const workdays = CalendarSet.workdays.map((day) => day.day);
        return res.json({ workdays });
    } catch (error) {
      next(error);
      return res.status(500).json({
        success: false,
        error: 'Internal server error',
      });
    }
};



const generateTimeSlots = (startTime: string, endTime: string, duration: number) => {
    const slots = [];
    let currentTime = moment(startTime, 'HH:mm');
  
    while (currentTime.isBefore(moment(endTime, 'HH:mm'))) {
        slots.push(currentTime.format('HH:mm'));
        currentTime.add(duration, 'minutes');
    }
  
    return slots;
};
  
const isTimeSlotBlocked = (slot: string, blockedTimes: { startTime: string, endTime: string }[]) => {
    return blockedTimes.some(blocked => {
        const blockedStart = moment(blocked.startTime, 'HH:mm');
        const blockedEnd = moment(blocked.endTime, 'HH:mm');
        const timeSlot = moment(slot, 'HH:mm');
        return timeSlot.isBetween(blockedStart, blockedEnd, undefined, '[)');
    });
};
export const getAvailableTimeSlots = async (req: any, res: Response, next: NextFunction): Promise<Response> => {
    const { userId } = req.params;
    const { timezone, date } = req.body; 
    
    try {
        // Fetch the calendar settings for the user
        const CalendarSet = await calendarSettings.findOne({ userId });
  
        if (!CalendarSet) {
            return res.status(404).json({ error: 'Calendar settings not found for the user' });
        }
  
        const { timeslots, customTimeslotDuration, workdays, timezone: userTimezone } = CalendarSet;
  
        // Extract the requested day from the date
        const requestedDay = moment(date).format('dddd').toLowerCase(); 
        const daySettings = workdays.find(day => day.day === requestedDay);
  
        if (!daySettings) {
            return res.status(400).json({ error: `The selected date (${requestedDay}) is not a working day for the user.` });
        }
  
        const { startTime: calendarStartTime, closingTime: calendarClosingTime } = daySettings;
  
        // Fetch blocked times for the user on the specified date
        const blockedTimes = await blockTime.find({
            userId,
            startDate: { $lte: new Date(date) },
            endDate: { $gte: new Date(date) }
        });
  
        // Convert calendar working hours to the user's timezone
        const userStartTimeInUserTZ = moment.tz(`${date} ${calendarStartTime}`, 'YYYY-MM-DD HH:mm', userTimezone);
        const userEndTimeInUserTZ = moment.tz(`${date} ${calendarClosingTime}`, 'YYYY-MM-DD HH:mm', userTimezone);
  
        const startTimeInExternalTZ = userStartTimeInUserTZ.clone().tz(timezone);
        const endTimeInExternalTZ = userEndTimeInUserTZ.clone().tz(timezone);
  
        // Determine timeslot duration
        let timeslotDuration: number;
        if (timeslots === 'custom') {
            if (customTimeslotDuration !== undefined) {
                timeslotDuration = customTimeslotDuration;
            } else {
                throw new Error('customTimeslotDuration is undefined');
            }
        } else {
            const parsedDuration = parseInt(timeslots, 10);
            if (!isNaN(parsedDuration)) {
                timeslotDuration = parsedDuration;
            } else {
                throw new Error('Invalid timeslot duration');
            }
        }
  
        // Generate all possible time slots based on the user's calendar settings
        const allTimeSlots = generateTimeSlots(startTimeInExternalTZ.format('HH:mm'), endTimeInExternalTZ.format('HH:mm'), timeslotDuration);
  
        // Convert blocked times to the external user's timezone
        const blockedTimesInExternalTZ = blockedTimes.map(blocked => {
            const blockedStartInUserTZ = moment.tz(`${date} ${blocked.startTime}`, 'YYYY-MM-DD HH:mm', userTimezone).tz(timezone);
            const blockedEndInUserTZ = moment.tz(`${date} ${blocked.endTime}`, 'YYYY-MM-DD HH:mm', userTimezone).tz(timezone);
            return { startTime: blockedStartInUserTZ.format('HH:mm'), endTime: blockedEndInUserTZ.format('HH:mm') };
        });
  
        // Filter out blocked timeslots
        const availableTimeSlots = allTimeSlots.filter(slot => !isTimeSlotBlocked(slot, blockedTimesInExternalTZ));
  
        return res.status(200).json({ success: true, availableTimeSlots });
        
    } catch (error) {
        console.error('Error fetching available time slots:', error);
        next(error);
        return res.status(500).json({
          success: false,
          error: 'Internal server error',
        });
    }
};
    
// export const deleteCalendarSettings = async (req: Request, res: Response, next: NextFunction): Promise<Response | void> => {
//     const { userId } = req.params; 

//     try {
//         const deletedSettings = await CalendarSettings.findOneAndDelete({ userId });
//         if (!deletedSettings) {
//             return res.status(404).json({
//                 success: false,
//                 message: 'Calendar settings not found for the user',
//             });
//         }

//         return res.status(200).json({
//             success: true,
//             message: 'Calendar settings deleted successfully',
//         });

//     } catch (error) {
//         next(error); 
//         return res.status(500).json({
//             success: false,
//             message: 'Internal server error',
//         });
//     }
// };