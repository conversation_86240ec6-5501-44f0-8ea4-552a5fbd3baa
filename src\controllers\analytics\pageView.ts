import {  Response, NextFunction } from 'express';
import pageView from '../../models/analytics/pageView';

export const trackPageView = async (req: any, res: Response, next: NextFunction): Promise<Response> => {
    const { pageUrl } = req.body;
    const userId = req.user._id
    try {
        const newView =  new pageView({
            userId,
            pageUrl
        })
        await newView.save()
        return res.status(201).json({
            success:true,
            message:"View Added"
        })
    } catch (error) {
        console.error('Error tracking page view:', error);
        next(error); 
        return res.status(500).json({ success: false, error: 'Server error' });
    }
}