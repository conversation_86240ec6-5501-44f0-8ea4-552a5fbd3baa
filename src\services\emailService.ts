import fs from 'fs';
import handlebars from 'handlebars';
import nodemailer from 'nodemailer';
import path from 'path';

const transporter = nodemailer.createTransport({
  host: process.env.EMAIL_SERVICE,
  secure: false,
  port: 587,
  auth: {
    user: process.env.EMAIL_LOGIN, // Your email id
    pass: process.env.EMAIL_PASSWORD, // Your password
  },
  // tls: {
  //   ciphers: 'SSLv3'
  // }
});

/**
 * Reads and compiles an email template with variables.
 * @param templateName The name of the template file (e.g., 'welcomeEmail.hbs')
 * @param variables The dynamic variables to inject into the template
 * @returns The compiled HTML string
 */
const compileTemplate = (templateName: string, variables: Record<string, any>): string => {
  const templatePath = path.resolve(`./src/emailTemplates/${templateName}`);
  const templateSource = fs.readFileSync(templatePath, 'utf8');
  const compiledTemplate = handlebars.compile(templateSource);
  return compiledTemplate(variables);
};

/**
 * Sends an email using the compiled template.
 * @param to Recipient email address
 * @param subject Email subject
 * @param templateName The name of the template file
 * @param variables The dynamic variables for the template
 */
export const sendEmail = async (
  to: string,
  subject: string,
  templateName: string,
  variables: Record<string, any>
): Promise<void> => {
  const html = compileTemplate(templateName, variables);

  const mailOptions = {
    from: {
        name: process.env.EMAIL_FROM_NAME || 'Puzzle Piece',
        address: process.env.EMAIL_FROM_ADDRESS || '<EMAIL>'
    },
    subject,
    html,
    to
  };

  await transporter.sendMail(mailOptions);
};
