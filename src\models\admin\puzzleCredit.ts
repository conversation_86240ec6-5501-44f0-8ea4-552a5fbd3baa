import mongoose, { Document, Schema } from 'mongoose';

export interface IPuzzleCredit extends Document {
  name:string,
  currency: string;
  creditAmount:number;
  amount: number;
}

const PuzzleCreditSchema: Schema = new Schema(
    {  
    name: { type: String, required: true },
      currency: { type: String, default: 'usd', required: true },
      creditAmount: { type: Number, required: true },
      amount: { type: Number, required: true }, //in usd
      
    },
    { timestamps: true }
  );
  
  export default mongoose.model<IPuzzleCredit>('PuzzleCredit', PuzzleCreditSchema);