import mongoose, { Schema, Document } from 'mongoose';

interface IleadCart extends Document {
    user: mongoose.Types.ObjectId;
    leads: mongoose.Types.ObjectId[]; 
    status: 'pending' | 'purchased';
    totalAmount:number;
    purchaseDate?: Date;
}

const leadCartSchema: Schema<IleadCart> = new Schema({
    user: { type: Schema.Types.ObjectId, ref: 'User', required: true },
    leads: [{ type: mongoose.Types.ObjectId, ref: 'Business_Lead'}],
    status: { type: String, enum: ['pending', 'purchased'], default: 'pending' },
    totalAmount:{type:Number, default:0},
    purchaseDate: { type: Date },
});

leadCartSchema.index({ user: 1, status: 1 }, { unique: true, partialFilterExpression: { status: 'pending' } });

export default mongoose.model<IleadCart>('LeadCart', leadCartSchema);

