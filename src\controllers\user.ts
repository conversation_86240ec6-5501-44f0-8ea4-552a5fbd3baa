import { NextFunction, Request, Response } from "express";
import jwt from "jsonwebtoken";
import mongoose from "mongoose";
import calendarSettings from "../models/calendarSettings";
import settings from "../models/settings";
import User from "../models/user";
import { sendEmail } from "../services/emailService";
import { createCustomer } from "../services/stripesService";
import { cloudinary } from "../utils/cloudinary";
import { generateToken } from "../utils/cypto";
import { generateUrlToken } from "../utils/urlTokenGenrator";
import {
  checkSubscriptionStatus,
  createUserSubscription,
} from "./subscriptions";
import { getObjectIdString, inlineSwitch, omit } from "../utils";
import PuzzlePrice from "../models/admin/puzzlePrice";
import PuzzleProduct from "../models/admin/puzzleProduct";
const JWT_SECRET = process.env.JSONSECRETKEY as string 

/**
 * @dev the flow
 * - register user
 * - send confirmation email after register
 * - create subscription for later payment
 * - after payment, user can log in.
 */
export const Register = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<Response> => {
  const session = await mongoose.startSession();
  session.startTransaction();
  const {
    first_name,
    last_name,
    username,
    email,
    password,
    planId,
    business_name,
  } = req.body;

  const urlToken = generateUrlToken();
  const bookingurl = `${process.env.FRONTENDURL}/bookings/${username}`;
  try {
    if (
      !first_name ||
      !last_name ||
      !username ||
      !email ||
      !password ||
      !planId ||
      !business_name
    ) {
      return res
        .status(400)
        .json({ success: false, error: "All fields are required" });
    }
    const userEmail = email.toLowerCase();
    const existingUser = await User.findOne({ 
      $or: [{ email: userEmail }, { username }] 
    }).session(session);
    if (existingUser) {
      await session.abortTransaction();
      return res.status(400).json({ 
        error: "User with that email or username already exists" 
      });
    }

    let userName = `${first_name} ${last_name}`;
    const customer = await createCustomer(userName, userEmail);
    const newUser = new User({
      stripesConsumerId: customer.id,
      first_name,
      last_name,
      username,
      email:userEmail,
      password,
      urlToken,
      business_name,
      planId: planId,
      bookingLink: bookingurl,
    });
    const savedUser = await newUser.save({ session });
    const userId = savedUser._id as string;

    let subscriptionResult;
    try {
      subscriptionResult = await createUserSubscription(
        userId,
        savedUser.stripesConsumerId,
        planId
      );
      savedUser.userSubscription = subscriptionResult.Usersubscription._id as mongoose.Types.ObjectId;
      await savedUser.save({ session });
    } catch (subscriptionError) {
      await session.abortTransaction();
      return res.status(400).json({
        success: false,
        error: `Subscription creation failed: ${subscriptionError}`
      });
    }

    // email confirmation request block
    const token = generateToken();
    newUser.emailConfirmationToken = token;
    newUser.emailConfirmationTokenExpires = new Date(
      Date.now() + 60 * 60 * 1000
    );
    await savedUser.save({ session });
    const confirmationLink = `${process.env.FRONTENDURL}/confirm-email/${token}?planId=${planId}`;
    await sendEmail(userEmail, "Confirm Your Email", "welcomeEmail.hbs", {
      name: userEmail,
      confirmationLink,
    });
   
   await session.commitTransaction();
    return res.status(201).json({
      message: "User registered successfully",
      userId: savedUser._id,
      clientSecret: subscriptionResult.clientSecret,
    });
  } catch (error) {
    await session.abortTransaction();
    console.error("Registration error:", error);
    next(error);
    return res
      .status(500)
      .json({ success: false, error: "Internal Server Error" });
  } finally {
    await session.endSession();
  }
};

export const Login = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<Response | void> => {
  const { email, password: pass } = req.body;
  try {
    if (!email || !pass) {
          return res.status(400).json({ error: "All fields are required" });
    }
    const userEmail = email.toLowerCase();
    const user = await User.findOne({ email: userEmail })
    if (!user) {
      return res.status(401).json({ error: "Invalid email or password" });
    }
    const isMatch = await user.comparePassword(pass);
    if (!isMatch)
      return res.status(401).json({ error: "Invalid email or password" });

    if (user.role ==='user' && !user.isEmailVerified) {
        const token = generateToken();
        user.emailConfirmationToken = token;
        user.emailConfirmationTokenExpires = new Date(
          Date.now() + 60 * 60 * 1000
        ); // 1-hour expiration
        await user.save();
        const confirmationLink = `${process.env.FRONTENDURL}/confirm-email/${token}`;
        // send another email to the user
        await sendEmail(userEmail, "Confirm Your Email", "welcomeEmail.hbs", {
          name: userEmail,
          confirmationLink,
        });
      // frontend should check for `403` status
      return res.status(403).json({
        error: "Email not confirmed. Please confirm your email first.",
      });
    }
    const token = jwt.sign({ userId: user._id }, JWT_SECRET, {
      expiresIn: "2d",
    });
    user.password = ''
    return res.status(200).json({
      message: "Login successful",
      token,
      // send the user object without password
      user:user
    });
  } catch (error) {
    next(error);
    return res
      .status(500)
      .json({ success: false, error: "Internal Server Error" });
  }
};

interface ProductResponse {
  name: string;
  price: number;
  description: string;
  recurring: "weekly" | "monthly" | "yearly";
  discount?: {
    id: string;
    name: string;
    coupon_code: string;
  };
  features: string[];
  yearlyPrice: number;
}

/**
 * @dev should be called just after user logs in;
 * @dev fetches payent details for user
 * @dev send 200 if admin
 */
export const CheckSubscriptionStatus = async (
  req: any,
  res: Response,
  next: NextFunction
): Promise<Response> => {
  const { _id: userId, planId, role } = req.user || {};
  // fetch plan here and send to user so we dont have another api call
  if (userId) {
    if (role === 'admin') return res.status(200).json({})
    const subscriptionStatus = await checkSubscriptionStatus(userId as string);
    // if subscription is not active, return 402
    if (subscriptionStatus.status === "incomplete")  {
      const products = await PuzzleProduct.find({ active: true });
      const productPrices = await PuzzlePrice.find();
      // productId(primary key) for prices and _id for products
      const [userSelectedPlan, userSelectedPlanPrices] = [
        products.find((product) => getObjectIdString(product._id) === planId),
        productPrices.filter((productPrice) => productPrice.productId.equals(planId)),
      ];
      if (!userSelectedPlan) return res.status(404).json({
        error: 'Subscription plan not found'
      });
      const { monthlyPrice, yearlyPrice } = (() => {
        return {
          monthlyPrice: userSelectedPlanPrices.find((price) => price.interval === 'month'),
          yearlyPrice: userSelectedPlanPrices.find((price) => price.interval === 'year'),
        }
      })()
      const fullUserSelectedPlan: ProductResponse & Record<string, any> = {
        _id: userSelectedPlan?._id,
        name: userSelectedPlan.name,
        price: monthlyPrice?.unitAmount || 0,
        description: userSelectedPlan.description || "",
        yearlyPrice: yearlyPrice?.unitAmount || 0,
        recurring: 'monthly',
        features: userSelectedPlan.features,
      };
      return res.status(402).json({
        error: subscriptionStatus.message,
        clientSecret: subscriptionStatus.clientSecret,
        plan: fullUserSelectedPlan,
        status: subscriptionStatus.status,
      });
      // the user has been verified
    } else return res.status(200).json({});
  } else return res.status(401).json({
    error: 'Login to make payment'
  });
};

export const VerifyEmail = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<Response> => {
  const { token } = req.params;
  try {
    const user = await User.findOne({
      emailConfirmationToken: token,
      emailConfirmationTokenExpires: { $gt: new Date() },
    });
    if (!user)
      return res.status(400).json({ error: "Invalid or expired token." });
    user.isEmailVerified = true;
    user.emailConfirmationToken = undefined;
    user.emailConfirmationTokenExpires = undefined;
    await user.save();
    return res.status(200).json({ message: "Email successfully confirmed." });
  } catch (err) {
    next(err);
    console.log("Error confirming email:", err);
    return res
      .status(500)
      .json({ success: false, error: "Internal Server Error" });
  }
};

export const requestPasswordReset = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<Response> => {
  const { email } = req.body;

  try {
    const userEmail = email.toLowerCase();
    const user = await User.findOne({ email:userEmail });

    if (!user) {
      return res.status(404).json({ message: "User not found." });
    }

    const token = generateToken();
    user.resetPasswordToken = token;
    user.resetPasswordExpires = new Date(Date.now() + 30 * 60 * 1000); // 30-minute expiration

    await user.save();

    const resetLink = `${process.env.FRONTENDURL}/reset-password/${token}`;
    await sendEmail(userEmail, "Reset Your Password", "resetPasswordEmail", {
      name: user.username,
      resetLink,
    });

    return res.status(200).json({ message: "Password reset email sent." });
  } catch (err) {
    next(err);
    console.log("reqset reset password error", err);
    return res
      .status(500)
      .json({ success: false, error: "Internal Server Error" });
  }
};

export const resetPassword = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<Response> => {
  const { token } = req.params;
  const { newPassword } = req.body;

  try {
    const user = await User.findOne({
      resetPasswordToken: token,
      resetPasswordExpires: { $gt: new Date() },
    });

    if (!user) {
      return res.status(400).json({ message: "Invalid or expired token." });
    }

    user.password = newPassword;
    user.resetPasswordToken = undefined;
    user.resetPasswordToken = undefined;
    await user.save();

    return res.status(200).json({ message: "Password reset successful." });
  } catch (err) {
    next(err);
    console.log("reset password error", err);
    return res
      .status(500)
      .json({ success: false, error: "Internal Server Error" });
  }
};

export const getUserDetailsWithCalendarSettings = async (
  req: any,
  res: Response,
  next: NextFunction
): Promise<Response | void> => {
  const userId = req?.user?._id; // Assuming req.user contains authenticated user data

  try {
    // Fetch user details
    const user = await User.findById(userId).select("-password"); // Exclude sensitive fields like password
    if (!user) {
      return res.status(404).json({
        success: false,
        message: "User not found",
      });
    }

    // Fetch the user's calendar settings
    const CalendarSettings = await calendarSettings.findOne({ userId });
    if (!CalendarSettings) {
      return res.status(404).json({
        success: false,
        message: "Calendar settings not found",
      });
    }
    const userSettings = await settings.findOne({ userId });
    if (!userSettings) {
      return res.status(404).json({
        success: false,
        message: "settings not found",
      });
    }

    return res.status(200).json({
      success: true,
      message: "User details and calendar settings fetched successfully",
      data: {
        user,
        calendarSettings: CalendarSettings,
        userSettings,
      },
    });
  } catch (error) {
    next(error);
    return res
      .status(500)
      .json({ success: false, error: "Internal Server Error" });
  }
};

export const UpdateUserDetails = async (
  req: any,
  res: Response,
  next: NextFunction
): Promise<Response | void> => {
  const userId = req?.user?._id; // Assuming req.user contains authenticated user data
  const {
    business_bio,
    timeFormat,
    dateFormat,
    first_name,
    last_name,
    username,
    email,
    state,
    country,
    business_name,
  } = req.body;
  try {
    const user = await User.findByIdAndUpdate(
      userId,
      {
        business_bio,
        timeFormat,
        dateFormat,
        first_name,
        last_name,
        username,
        email,
        state,
        country,
        business_name,
      },
      { new: true }
    );
    if (!user) {
      return res.status(404).json({
        success: false,
        message: "User not found",
      });
    }
    return res.status(200).json({
      success: true,
      message: "User details updated successfully",
    });
  } catch (error) {
    next(error);
    return res
      .status(500)
      .json({ success: false, error: "Internal Server Error" });
  }
};

/**
 * @dev This should be used by the profile page or so
 * @dev the one for digital profile is different
 */
export const uploadProfileImage = async (
  req: any,
  res: Response,
  next: NextFunction
): Promise<Response> => {
  const userId = req?.user?._id;
  if (!req.file) {
    return res.status(400).json({
      success: false,
      error: "No file uploaded",
    });
  }
  try {
    const imageUrl = req.file?.path;
    const imagePhotoId = req.file?.filename || "";
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({
        success: false,
        error: "User not found",
      });
    }
    const oldPublicId = user.user_photo_PublicId;
    if (oldPublicId) {
      await cloudinary.v2.uploader.destroy(oldPublicId);
    }
    console.log("imageUrl", imageUrl);
    user.user_photo = imageUrl;
    user.hasProfileImage = true;
    user.user_photo_PublicId = imagePhotoId;
    await user.save();
    return res.status(200).json({
      success: true,
      message: "Profile image uploaded successfully",
    });
  } catch (error) {
    console.log("upload-image-error:", error);
    next(error);
    return res
      .status(500)
      .json({ success: false, error: "Internal Server Error" });
  }
};
