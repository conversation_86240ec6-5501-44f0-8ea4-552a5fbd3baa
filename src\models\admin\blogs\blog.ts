import mongoose, { Document, Schema } from 'mongoose';

interface IBlog extends Document {
  title: string;
  shortDescription: string;
  authorName: string;
  longDescription: string;
  date: Date; 
  imageUrl: string;
  hasImage: boolean;
  imagePublicId:string;
  tags: string[];
  uniqueKey: string;
  previousSlugs: string[];
}

const BlogSchema = new Schema<IBlog>({
  title: { type: String, required: true, unique: true},
  shortDescription: { type: String, required: true },
  authorName: { type: String, required: true },
  longDescription: { type: String, required: true },
  date: { type: Date, default: Date.now },
  imageUrl: { type: String, required: false },
  hasImage: { type: Boolean, required: false },
  imagePublicId: { type: String, required: false },
  tags: { type: [String], required: false },
  uniqueKey: { type: String, unique: true , index: true},
  previousSlugs: { type: [String], default: [] }, 
  // Add previousSlugs field
});
BlogSchema.pre('save', function(next) {
  if (!this.uniqueKey) {
    const titleSlug = this.title
    .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .trim();
    
    // const now = new Date();
    // const year = now.getFullYear();
    // const month = String(now.getMonth() + 1).padStart(2, '0');
    // const day = String(now.getDate()).padStart(2, '0');
    // const dateString = `${year}-${month}-${day}`;
    
    this.uniqueKey = `${titleSlug}`;
  }
  next();
});

export default mongoose.model<IBlog>('Blog', BlogSchema);
