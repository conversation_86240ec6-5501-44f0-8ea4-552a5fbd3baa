import {Response, NextFunction } from 'express';
import { createAppleAccount, getGoogleAuthUrl, getOutlookAuthUrl, getTokensFromCode, refreshAccessToken } from '../services/oauthService'; // Implement OAuth functions
import { GoogleEventsDetails, addEventToGoogleCalendar, addEventToOutlookCalendar } from '../services/calendarService';
import externalAccount from '../models/externalAccount';
import User from '../models/user';
import { RegisterStripeAccount } from '../services/stripesService';
import { syncUpsertBookings } from './booking';
import mongoose from 'mongoose';

export const connectExternalAccount = async (req: any, res: Response, next: NextFunction) => {
  const { service,email, appPassword } = req.query; 
  const userId = req.user?._id
  try {
    console.log(service)
    let authUrl;
    if (service === 'google') {
      authUrl = getGoogleAuthUrl();  
    } else if (service === 'outlook') {
      authUrl = await getOutlookAuthUrl();
      console.log(authUrl)
    }else if(service === 'apple' && email && appPassword ){
      const response = await createAppleAccount(email, appPassword)
      if (response){
        const ExternalAccount = await externalAccount.findOneAndUpdate(
          { userId, name: service },
          {
            connect: true,
            activate: true, 
            connectedEmail:email,
            appleCredentials:{
              username:email,
              appPassword,
              calendarUrl:response.calendarUrl
            }
          },
          { new: true, upsert: true } 
        );
        return res.status(200).json({ success: true, externalAccount: ExternalAccount });
      }else{
        return res.status(400).json({
          success:false,
          error:'Unable to authenticate credentials'
        })
      }
    } else {
      return res.status(400).json({ error: 'Invalid service type' });
    }

    return res.status(200).json({ authUrl });
  } catch (error) {
    console.error('Error generating auth URL:', error);
    next(error);
  }
};

export const handleOAuthCallback = async (req: any, res: Response, next: NextFunction) => {
  const userId = req.user?._id;
  const { code, service} = req.query;
  try {
    const { accessToken, refreshToken, expiresAt,email } = await getTokensFromCode(code, service as string);
    if (service === 'stripe'){
      const reqUser = await User.findById(userId)
      if(!reqUser){
        return res.status(404).json({
          error:"User not found",
          success:false
        })
      }
      reqUser.stripesAccountId = email
      await reqUser.save()
      // const USERID: string = (userId as string).toString(); 
      // await RegisterStripeAccount(email,USERID)
    }

    const ExternalAccount = await externalAccount.findOneAndUpdate(
      { userId, name: service },
      {
        accessToken,
        refreshToken,
        tokenExpiresAt: expiresAt,
        connect: true,
        activate: true,  
        connectedEmail: service === 'stripe' ? req.user?.email : email
      },
      { new: true, upsert: true } 
    );
    await syncUpsertBookings(userId)
    return res.status(200).json({ success: true, externalAccount: ExternalAccount });
  } catch (error) {
    console.error('Error handling OAuth callback:', error);
    next(error);
    return res.status(500).json({ success: false, error:"Internal Server Error" });
  }
};

export const disconnectExternalAccount = async (req: any, res: Response, next: NextFunction) => {
  const userId = req.user?._id;
  const { service } = req.body; 

  try {
    const ExternalAccount = await externalAccount.findOneAndUpdate(
      { userId, name: service },
      {
        connect: false,
        activate: false,
        accessToken: null,
        refreshToken: null,
        tokenExpiresAt: null,
        connectedEmail:null
      },
      { new: true }
    );

    if (!ExternalAccount) {
      return res.status(404).json({ error: 'External account not found' });
    }

    return res.status(200).json({ success: true, message: `${service} account disconnected`, externalAccount: ExternalAccount });
  } catch (error) {
    console.error('Error disconnecting account:', error);
    next(error);
  }
};

export const toggleExternalAccountActivation = async (req: any, res: Response, next: NextFunction) => {
  const userId = req.user?._id;
  const { service, activate } = req.body;
  try {
    const ExternalAccount = await externalAccount.findOneAndUpdate(
      { userId, name: service}, 
      { activate },  
      { new: true } 
    );

    if (!ExternalAccount) {
      return res.status(404).json({ success: false, error: 'External account not found or not connected' });
    }

    const message = activate 
      ? `${service} account activated successfully`
      : `${service} account deactivated successfully`;

    return res.status(200).json({ success: true, message});
  } catch (error) {
    console.error('Error toggling account activation:', error);
    next(error);
    return res.status(500).json({ success: false, error: 'Internal server error' });
  }
};

export const checkStripeConnected = async (req: any, res: Response, next: NextFunction) => {
  try {
    const userId = req.user?._id;
    const ExternalAccount = await externalAccount.findOne({ userId, name: "stripe",connect: true, activate: true});
    if(ExternalAccount){
      return res.status(200).json({
        success:true,
        status:true
      })
    }else{
      return res.status(404).json({
        success:false,
        status:false,
        error: 'Stripe account not connected'
      })
    }

  } catch (error) {
    console.error('Error check stripe connected account:', error);
    next(error);
    return res.status(500).json({ success: false, error: 'Internal server error' });
  }
}


export const syncSchedulesToExternalCalendars = async (req: any, res: Response, next: NextFunction) => {
  const userId = req.user?._id;
  try {
    // const googleEvent:GoogleEventsDetails = {
    //   title: "Meeting",
    //   location: "Conference Room",
    //   description: "Discussion about product roadmap.",
    //   startDateTime: new Date("2023-10-25T16:00:00Z"),
    //   endDateTime: new Date("2023-10-25T17:00:00Z"),
    //   timeZone:"Europe/London"
    // }
    // const ExternalAccount = await externalAccount.findOne({ userId, name: "outlook",connect: true, activate: true});

    // if (!ExternalAccount) {
    //   return res.status(404).json({ success: false, error: 'Account not found' });
    // }
    // // const googleAccessToken = await refreshAccessToken(ExternalAccount, "google")
    // // if (googleAccessToken) {
    // //   await addEventToGoogleCalendar(googleAccessToken, googleEvent);
    // // }

    const ExternalAccount = await externalAccount.findOne({ userId, name: "outlook",connect: true, activate: true});
    const outlookEvent = {
      title: "Sample Event",
      description: "This is a sample event description.",
      startDateTime: "2023-11-15T09:00:00",
      endDateTime: "2023-11-15T10:00:00",
      location: "Sample Location",
      timeZone: "Pacific Standard Time"
    };
    if (!ExternalAccount) {
      return res.status(404).json({ success: false, error: 'Account not found' });
    }
    const outlookAccessToken = await refreshAccessToken(ExternalAccount, "outlook")
    if (outlookAccessToken) {
      await addEventToOutlookCalendar(outlookAccessToken, outlookEvent);
    }

    return res.status(200).json({ success: true, message: 'Schedules synced successfully'});
  } catch (error) {
    console.error('Error syncing schedules:', error);
    next(error);
    return res.status(500).json({ success: false, error: 'Internal server error' });
  }
};

export const GetUserExternalAccounts = async (req: any, res: Response, next: NextFunction) => {
  const userId = req.user?._id;
  try {
    await createDefaultExternalAccounts(userId)
    const externalAccounts = await externalAccount.find({userId}).select('-accessToken -refreshToken -__v');
    return res.status(200).json({ success: true, externalAccounts });
  } catch (error) {
    console.error('Error in getting external accounts:', error);
    next(error);
    return res.status(500).json({ success: false, error: 'Internal server error' });
  }
};


export const createDefaultExternalAccounts = async (userId: string,session?:mongoose.ClientSession) => {
  try {
    const existingAccounts = await externalAccount.find({ userId });
    const existingServices = existingAccounts.map(account => account.name);

    const servicesToCreate = (["google", "outlook","stripe"] as const).filter(
      service => !existingServices.includes(service)
    ) as Array<"google" | "outlook">;
    
    const newAccounts = servicesToCreate.map(service => ({
      userId,
      name: service,
      connect: false,
      activate: false,
    }));
    if (newAccounts.length > 0) {
     if (session) {
        await externalAccount.insertMany(newAccounts, { session });
      } else {
        await externalAccount.insertMany(newAccounts);
      }
    }

    // console.log(`Default external accounts created for user ${userId}.`);
  } catch (error) {
    console.error('Error creating default external accounts:', error);
    throw error;
  }
};

