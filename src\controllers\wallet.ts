import { NextFunction, Response } from "express";
import { Transaction } from "../models/wallet";
import puzzleCredit from "../models/admin/puzzleCredit";
import {
  createStripePaymentIntentWithMethod,
  retrievePaymentIntentofInvoice,
} from "../services/stripesService";
import paymentMethod from "../models/paymentMethod";
import User from "../models/user";
import { addUserCredit } from "../utils/webhooksFunctions";
import paymentIntent from "../models/stripes/paymentIntent";
import { convertToMinorUnits } from "../utils/stripes";
import { applyPromoCode } from "./admin/puzzlePromoCode";

export const purchaseCredits = async (
  req: any,
  res: Response,
  next: NextFunction
): Promise<Response> => {
  const userId = req.user?._id;
  const { creditId, promocode, paymentId } = req.body;
  try {
    const user = await User.findById(userId);
    if (!user) {
      return res.status(400).json({
        success: false,
        error: "User not found",
      });
    }
    const creditItem = await puzzleCredit.findById(creditId);
    if (!creditItem) {
      return res.status(400).json({
        success: false,
        error: "Purchasing credit item is not found",
      });
    }

    // Check if the payment method exists
    const userPayment = await paymentMethod.findById(paymentId);
    if (!userPayment) {
      return res.status(400).json({
        success: false,
        error: "Payment method item is not found",
      });
    }

    const metadata = {
      creditId,
      userId: userId.toString(),
    };
    let promoamount: number;
    if (promocode) {
      const promo = await applyPromoCode(promocode, creditItem.amount);
      promoamount = promo.discountAmount;
    } else {
      promoamount = creditItem.amount;
    }
    // Create Stripe payment intent
    const newPaymentIntent = await createStripePaymentIntentWithMethod(
      user.stripesConsumerId,
      convertToMinorUnits(promoamount),
      creditItem.currency,
      userPayment.stripePaymethodId,
      metadata,
      user.email
    );
    const newpaymentintentData = new paymentIntent({
      stripePaymentIntentId: newPaymentIntent.id,
      intentType: "credit",
      intentTypeId: creditItem._id,
      email: user.email,
      userId: user._id,
      clientSecret: newPaymentIntent.client_secret,
      status: newPaymentIntent.status,
    });
    await newpaymentintentData.save();

    if (newPaymentIntent) {
      return res.status(200).json({
        success: true,
        message: "Payment intent created",
        stripePaymentMethodId: userPayment.stripePaymethodId,
        clientSecret: newPaymentIntent.client_secret,
      });
    } else {
      return res.status(500).json({
        success: false,
        error: "Failed to create payment intent",
      });
    }
  } catch (error: any) {
    next(error);
    console.error("Error purchasing credits:", error);
    return res
      .status(500)
      .json({ error: "Error purchasing credits", message: error?.message });
  }
};

export const spendCredits = async (
  req: any,
  res: Response,
  next: NextFunction
): Promise<Response> => {
  const userId = req.user?._id;
  const { amount, reason } = req.body;

  try {
    const user = await User.findById(userId);
    if (!user) {
      return res.status(400).json({
        success: false,
        error: "User not found",
      });
    }

    if (user.totalCredit < amount) {
      return res.status(400).json({ error: "Insufficient credits" });
    }

    user.totalCredit -= amount;
    await user.save();

    await Transaction.create({
      userId,
      transactionType: "debit",
      amount,
      reason,
    });

    return res.status(200).json({ message: "Credits used successfully" });
  } catch (error) {
    next(error);
    console.log("using credit eror", error);
    return res.status(500).json({ error: "Error using credits" });
  }
};

export const FetchPaymentMethod = async (
  req: any,
  res: Response,
  next: NextFunction
): Promise<Response> => {
  try {
    const userId = req.user?._id;
    const paymentMethods = await paymentMethod.find({ userId });
    return res.status(200).json({
      success: true,
      paymentMethods,
    });
  } catch (error) {
    next(error);
    console.log("fetch payment method eror", error);
    return res.status(500).json({ error: "Error using credits" });
  }
};

export const PurchaseCreditOnSuccess = async (
  req: any,
  res: Response,
  next: NextFunction
): Promise<Response> => {
  try {
    // const userId = req.user?._id
    const { paymentIntentId } = req.body;
    const existStripePaymentIntent = await retrievePaymentIntentofInvoice(
      paymentIntentId
    );
    const creditId = existStripePaymentIntent.metadata.creditId;
    const userId = existStripePaymentIntent.metadata.userId;
    const result = await addUserCredit(userId, creditId);
    const paymentIntentData = await paymentIntent.findOne({
      stripePaymentIntentId: paymentIntentId,
      userId,
      // intentType:'credit'
    });
    if (paymentIntentData) {
      paymentIntentData.status = existStripePaymentIntent.status;
      await paymentIntentData.save();
    }
    return res.status(result.status).json({
      success: result.success,
      message: result.message,
      error: result.error,
    });
  } catch (error) {
    next(error);
    console.log("fetch payment method eror", error);
    return res.status(500).json({ error: "Error using credits" });
  }
};
