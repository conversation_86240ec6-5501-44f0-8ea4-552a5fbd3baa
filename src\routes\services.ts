import { Router } from 'express';
import { createService, deleteService, getBookingServices, getDirectBookingService, getService, getServiceById, updatedService } from '../controllers/services';
import { authMiddleware } from '../middleware/authService';
const router: Router = Router();

router.post('/create-service', authMiddleware,createService);
router.put('/service/:id', authMiddleware,updatedService);
router.delete('/delete-service/:id', authMiddleware,deleteService);
router.get('/service', authMiddleware,getService);
router.get('/service/:id', authMiddleware,getServiceById);
router.get('/service/bookings/:username',getBookingServices);
router.get('/service/bookings/direct/:urlToken', getDirectBookingService);
export default router;