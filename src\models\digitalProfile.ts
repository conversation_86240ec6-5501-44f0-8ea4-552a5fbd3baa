import mongoose, { Schema, Document } from 'mongoose';

export interface IWebsiteLink {
  title: string;
  url: string;
  icon: string | null;
  isActive: boolean;
  buttonStyle: {
    id: string;
  };
}

export interface IDigitalProfile extends Document {
  userId: mongoose.Types.ObjectId; // Reference to the user
  profileUrl:string;
  websiteLinks: IWebsiteLink[];
  imageUrl: string | null;
  hasImage: boolean;
  imagePublicId:string;
  details: {
    title: string;
    bio: string;
  };
  styles: {
    backgroundColor: string;
  };
}

const WebsiteLinkSchema: Schema = new Schema({
  title: { type: String, required: true },
  url: { type: String, required: true,
    validate: {
        validator: function(v: string) {
            return /^https?:\/\/.+\..+/.test(v);
        },
        message: 'Invalid URL format.',
    },
  },
  icon: { type: String, default: null },
  isActive: { type: Boolean, default: true },
  buttonStyle: {
    id: { type: String, required: true },
  },
});

const DigitalProfileSchema: Schema = new Schema(
  {
    userId: { type: mongoose.Types.ObjectId, ref: 'User', required: true, unique:true},
    websiteLinks: { type: [WebsiteLinkSchema], default: [] },
    imageUrl: { type: String, default: null },
    hasImage: { type: Boolean, required: false },
    imagePublicId: { type: String, required: false },
    profileUrl: { type: String,required:true, unique:true},
    details: {
      title: { type: String, required: true },
      bio: { type: String, required: true },
    },
    styles: {
      backgroundColor: { type: String, required: true },
    },
  },
  { timestamps: true }
);

export default mongoose.model<IDigitalProfile>('DigitalProfile', DigitalProfileSchema);
