import cloudinary from 'cloudinary';
import multer, { Multer } from 'multer';
import { CloudinaryStorage, } from 'multer-storage-cloudinary';
import dotenv from 'dotenv';
dotenv.config();
if(!process.env.CLOUDINARY_CLOUD_NAME || !process.env.CLOUDINARY_API_KEY || !process.env.CLOUDINARY_API_SECRET){
  throw new Error('Missing environment variables');
}
cloudinary.v2.config({
  cloud_name: process.env.CLOUDINARY_CLOUD_NAME as string,
  api_key: process.env.CLOUDINARY_API_KEY as string,
  api_secret: process.env.CLOUDINARY_API_SECRET as string,
});


const profileImageStorage = new CloudinaryStorage({
  cloudinary: cloudinary.v2,
  params: {
    folder: 'ProfileImages',
    allowed_formats: ['jpg', 'png'] as string[],
  } as any
});

const blogImageStorage = new CloudinaryStorage({
  cloudinary: cloudinary.v2,
  params: {
    folder: 'BlogImages',
    allowed_formats: ['jpg', 'png'],
  } as any
});
// Create Multer upload handlers
const profileImageUpload: Multer = multer({ storage: profileImageStorage });
const blogImageUpload: Multer = multer({ storage: blogImageStorage });

// Export modules
export { cloudinary, blogImageUpload, profileImageUpload };
