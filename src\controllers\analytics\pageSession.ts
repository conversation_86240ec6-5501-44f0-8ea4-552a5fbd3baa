import {  Response, NextFunction } from 'express';
import pageSession from '../../models/analytics/pageSession';

export const trackPageSession = async (req: any, res: Response, next: NextFunction): Promise<Response> => {
    const { startTime,endTime,pageUrl} = req.body;
    const userId = req.user._id
    try {
        const newSession =  new pageSession({
            userId,
            pageUrl,
            startTime,
            endTime
        })
        await newSession.save()
        return res.status(201).json({
            success:true,
            message:"Page Session Added"
        })
    } catch (error) {
        console.error('Error tracking page session:', error);
        next(error); 
        return res.status(500).json({ success: false, error: 'Server error' });
    }
}