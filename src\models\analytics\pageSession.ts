import mongoose, { Schema, Document } from 'mongoose';

interface IPageSession extends Document {
    userId: mongoose.Types.ObjectId;
    pageUrl:string;
    startTime:Date;
    endTime:Date;
}

const pageSessionSchema: Schema = new Schema({
    userId: { type: Schema.Types.ObjectId, ref: 'User', required: true },
    pageUrl: { type: String, required: true },
    startTime: { type: Date, required: true },
    endTime: { type: Date },
},{ timestamps: true });
export default mongoose.model<IPageSession>('PageSession', pageSessionSchema);
