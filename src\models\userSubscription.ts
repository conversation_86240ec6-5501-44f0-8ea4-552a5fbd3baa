import mongoose, { Document, Schema } from 'mongoose';

export interface IUserSubscription extends Document {
  user: mongoose.Types.ObjectId;
  subscriptionPlan:mongoose.Types.ObjectId;
  stripeSubscriptionId: string;
  status: 'incomplete'| 'incomplete_expired'| 'trialing'| 'active'| 'past_due'|'canceled'|'unpaid'|'paused';
  startDate: Date;
  endDate: Date;
  nextBillingDate?: Date;
}

const UserSubscriptionSchema: Schema = new Schema(
  {
    user: { type: mongoose.Types.ObjectId, ref: 'User', required: true,unique:true},
    subscriptionPlan: { type: mongoose.Types.ObjectId, ref: 'PuzzlePrice', required: true },
    stripeSubscriptionId: { type: String, required: true },
    status: {
      type: String,
      enum: ['incomplete', 'incomplete_expired', 'trialing', 'active', 'past_due','canceled','unpaid','paused'],
      default: 'incomplete',
    },
    startDate: { type: Date, required: true },
    endDate: { type: Date, required: true },
    nextBillingDate: { type: Date },
  },
  { timestamps: true }
);

export default mongoose.model<IUserSubscription>('UserSubscription', UserSubscriptionSchema);
