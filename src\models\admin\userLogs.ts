import mongoose, { Schema, Document } from "mongoose";

export interface IUserLog extends Document {
  type: string; 
  description: string; 
  ip: string; 
  user_agent: string; 
  timestamp: Date; 
  userId: mongoose.Types.ObjectId; 
}

const UserLogSchema = new Schema<IUserLog>({
  type: { type: String, required: true },
  description: { type: String, required: true },
  ip: { type: String, required: true },
  user_agent: { type: String, required: true },
  timestamp: { type: Date, default: Date.now },
  userId: { type: Schema.Types.ObjectId, ref: "User", required: true },
});

export default  mongoose.model<IUserLog>("UserLog", UserLogSchema);

