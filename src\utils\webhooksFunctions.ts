import puzzleCredit from "../models/admin/puzzleCredit"
import User from "../models/user"

export const addUserCredit =  async (userId:string, creditId:string)=>{
    try {
        const user = await User.findById(userId)
        if(!user){
            return {
                status: 404,
                success: false,
                error: "User to be credit is not found"
            };
        }
        const credit = await puzzleCredit.findById(creditId)
        if(!credit){
            return {
                status: 404,
                success: false,
                error: "Credit object is not found"
            };
        }
        const ammountTobeCredit = credit.creditAmount
        user.totalCredit += ammountTobeCredit
        await user.save()
        return {
            status:200,
            success: true,
            message: "Added Successfully"
        };
    } catch (error:any) {
        return {
            status: 500,
            success: false,
            error: `Error adding user credit: ${error.message || error}`
        };
    }
}


// export const updateUsersPaymentMethod =  async (userId:string, creditId:string)=>{
//     try {
//         const user = await User.findById(userId)
//         if(!user){
//             throw new Error('user to be credit is not found')
//         }
//         

//     } catch (error) {
//         console.log("adding user credit error",error)
//     }
// }