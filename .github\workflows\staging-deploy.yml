name: Staging Deployment

on:
  push:
    branches:
      - dev

jobs:
  deploy:
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout repository
        uses: actions/checkout@v3

      - name: Setup SSH key
        run: |
          mkdir -p ~/.ssh
          echo "${{ secrets.SSH_PRIVATE_KEY }}" > ~/.ssh/id_rsa
          chmod 600 ~/.ssh/id_rsa
          ssh-keyscan -H ${{ secrets.SERVER_HOST }} >> ~/.ssh/known_hosts
          
      - name: Sync files with rsync (preserve server files)
        run: |
          rsync -avz --delete \
            --exclude='.env' \
            ./ ${{ secrets.SERVER_USERNAME }}@${{ secrets.SERVER_HOST }}:/puzzle-piece/staging/backend
          
          
      - name: Build and Deploy
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.SERVER_HOST }}
          username: ${{ secrets.SERVER_USERNAME }}
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          script: |
            cd /puzzle-piece/staging/backend
            # Stop and remove existing containers/images if they exist
            docker compose down --remove-orphans || true
            docker system prune -f || true
            # Build and start the new containers
            docker compose up -d --build --force-recreate
            # Wait a moment for containers to start
            sleep 10
            # Check container status
            docker ps -a
            # Show logs for debugging
            docker compose logs --tail=50