import {  Response, NextFunction } from 'express';
import category, { ICategory } from '../models/category';

export const createCategory = async (req: any, res: Response, next: NextFunction): Promise<Response> => {
    const userId = req.user?._id
    const { name }: ICategory = req.body;

    try {
        const newCategory = new category({ userId, name });
        const savedCategory = await newCategory.save();
        
        return res.status(201).json({
            success: true,
            message: 'Category created successfully',
            category: savedCategory
        });
    } catch (error) {
        next(error);
        return res.status(500).json({ success: false, error: 'Internal Server Error' });
    }
};


export const getCategories = async (req: any, res: Response, next: NextFunction): Promise<Response> => {
    try {
        const categories = await category.find({ userId: req.user?._id });
        return res.status(200).json({ success: true, categories });
    } catch (error) {
        next(error);
        return res.status(500).json({ success: false, error: 'Internal Server Error' });
    }
};

export const getCategoryById = async (req: any, res: Response, next: NextFunction): Promise<Response> => {
    const userId = req.user?._id
    const { id } = req.params;

    try {
        const Category = await category.findOne({_id:id, userId:userId});
        
        if (!Category) {
            return res.status(404).json({ success: false, error: 'Category not found' });
        }

        return res.status(200).json({ success: true, Category });
    } catch (error) {
        next(error);
        return res.status(500).json({ success: false, error: 'Internal Server Error' });
    }
};

// UPDATE
export const updateCategory = async (req: any, res: Response, next: NextFunction): Promise<Response> => {
    const userId = req.user?._id
    const { id } = req.params;
    const { name }: ICategory = req.body;

    try {
        const updatedCategory = await category.findOneAndUpdate(
            {_id:id, userId:userId},
            { name },
            { new: true, runValidators: true }
        );
        
        if (!updatedCategory) {
            return res.status(404).json({ success: false, error: 'Category not found' });
        }

        return res.status(200).json({ success: true, message: 'Category updated successfully', category: updatedCategory });
    } catch (error) {
        next(error);
        return res.status(500).json({ success: false, error: 'Internal Server Error' });
    }
};

export const deleteCategory = async (req: any, res: Response, next: NextFunction): Promise<Response> => {
    const { id } = req.params;
    const userId = req.user?._id

    try {
        const deletedCategory = await category.findOneAndDelete({_id:id, userId:userId})
        if (!deletedCategory) {
            return res.status(404).json({ success: false, error: 'Category not found' });
        }
        return res.status(200).json({ success: true, message: 'Category deleted successfully' });
    } catch (error) {
        next(error);
        return res.status(500).json({ success: false, error: 'Internal Server Error' });
    }
};
