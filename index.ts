import express, { Application } from 'express';
import mongoose from 'mongoose';
import dotenv from 'dotenv';
import session from "express-session";
import rateLimit from "express-rate-limit";
import cors from 'cors';
import requestLogger from './src/utils/requestLogger';
import { createServer } from 'http'; // Use HTTP to create a server
import { Server as SocketIOServer } from 'socket.io'; // Properly typed Socket.IO
import { socketService } from './src/services/socketService';
import userRoutes from './src/routes/user';
import servicesRoutes from './src/routes/services'
import categoryRoutes from './src/routes/category'
import calendarRoutes from './src/routes/calendarSettings'
import bookingRoutes from './src/routes/booking'
import digitalProfile from './src/routes/digitalProfile';
import settingRoutes from './src/routes/settings'
import ExternalRoutes from './src/routes/externalAccounts'
import adminRoutes from './src/routes/admin/adminRoutes'
import subscriptionRoutes from './src/routes/subscription'
import LeadRoutes from './src/routes/businessLeads'
import WalletRoutes from './src/routes/wallet'
import businessClientRoutes from './src/routes/businessClient'
import paymentIntentRoutes from './src/routes/admin/adminRoutes'
import analyticsRoutes from './src/routes/analytics'
import resourcesRoutes from './src/routes/resources'
import blockSuspiciousIPs from './src/middleware/blockIp';
import blog from './src/models/admin/blogs/blog';
dotenv.config();

// Initialize Express application
const app: Application = express();

//MONGODB CONFIGURATION
const mongourl = process.env.MONGODB_URL || ''
console.log(mongourl)
mongoose.connect(mongourl);
mongoose.connection.on('connected', () => {
	console.log('Connected to mongo DB!...');
});

mongoose.connection.on('error', (err: any) => {
	console.log('Error Connecting to mongo!...', err);
});


const server = createServer(app);


// Initialize Socket.IO
const io = new SocketIOServer(server, {
  cors: {
    origin: process.env.FRONTENDURL, // Your client domain
    methods: ["GET", "POST"],
    allowedHeaders: ['Content-Type', 'Authorization'],
  }
});

// Initialize Socket.IO service
socketService(io);

app.use(cors({
  origin: (origin, callback) => {
    const allowedOrigins = [
      process.env.FRONTENDURL,
      process.env.FRONTENDURL2,
      // Add more URLs here
    ].filter(Boolean);
    
    // Allow requests with no origin (like mobile apps or curl requests)
    if (!origin) return callback(null, true);
    
    if (allowedOrigins.includes(origin)) {
      callback(null, true);
    } else {
      callback(new Error('Not allowed by CORS'));
    }
  },
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization'],
}));
const sessionSecret: string = process.env.SESSION_SECRET || 'default_secret_value';
const sessionConfig = {
  secret: sessionSecret,
  resave: false,
  saveUninitialized: false,
  cookie: {
      secure: false, 
  }
}
if (process.env.NODE_ENV === 'production') {
  app.set('trust proxy', 1) 
  sessionConfig.cookie.secure = true 
}
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // Limit each IP to 100 requests per window
  message: "Too many requests from this IP, please try again later.",
});


// const addUniqueKeyToExistingBlogs = async () => {
//   try {
//     // Find all blogs without uniqueKey
//     const blogsWithoutUniqueKey = await blog.find({ 
//       $or: [
//         { uniqueKey: { $exists: false } },
//         { uniqueKey: null },
//         { uniqueKey: '' }
//       ]
//     });

//     console.log(`Found ${blogsWithoutUniqueKey.length} blogs without unique keys`);

//     for (const blogs of blogsWithoutUniqueKey) {
//       // Create unique key manually
//       const titleSlug = blogs.title
//         .toLowerCase()
//         .replace(/[^a-z0-9\s-]/g, '')
//         .replace(/\s+/g, '-')
//         .trim();

//       const blogDate = new Date(blogs.date);
//       const year = blogDate.getFullYear();
//       const month = String(blogDate.getMonth() + 1).padStart(2, '0');
//       const day = String(blogDate.getDate()).padStart(2, '0');
//       const dateString = `${year}-${month}-${day}`;
      
//       let uniqueKey = `${titleSlug}-${dateString}`;
      
//       // Check if this unique key already exists, if so, add a counter
//       let counter = 1;
//       let finalUniqueKey = uniqueKey;
//       while (await blog.findOne({ uniqueKey: finalUniqueKey })) {
//         finalUniqueKey = `${uniqueKey}-${counter}`;
//         counter++;
//       }
      
//       // Update the blog with the unique key
//       await blog.findByIdAndUpdate(blogs._id, { uniqueKey: finalUniqueKey });
//       console.log(`Updated blog "${blogs.title}" with unique key: ${finalUniqueKey}`);
//     }
    
//     console.log('Migration completed successfully');
//   } catch (error) {
//     console.error('Error during migration:', error);
//     throw error;
//   }
// };

// console.log(sessionConfig)
app.use(session(sessionConfig));
app.use(express.json());
app.use(requestLogger);
// app.use(limiter);
app.use(blockSuspiciousIPs)

//Routes
app.use('/api',userRoutes)
app.use('/api',servicesRoutes)
app.use('/api',categoryRoutes)
app.use('/api',calendarRoutes)
app.use('/api',bookingRoutes)
app.use('/api',digitalProfile)
app.use('/api',settingRoutes)
app.use('/api',ExternalRoutes)
app.use('/api',adminRoutes)
app.use('/api',subscriptionRoutes)
app.use('/api',LeadRoutes)
app.use('/api',WalletRoutes)
app.use('/api',businessClientRoutes)
app.use('/api',paymentIntentRoutes)
app.use('/api',analyticsRoutes)
app.use('/api',resourcesRoutes)
app.get('/health', (req, res) => {
  res.json({ status: 'OK', message: 'Server is running' });
});

const PORT = process.env.PORT || 5000;
server.listen(PORT, () => {
  console.log(`Server is running on http://localhost:${PORT}`);
});
