import settings from "../models/settings";
import {  Response, NextFunction } from 'express';

export const updateBookingsettings = async (req: any, res: Response, next: NextFunction): Promise<Response> => {
    const userId = req.user?._id; // Assuming authentication middleware sets req.user
    const { textColor, backgroundColor, backgroundImageUrl, description, buttonColor } = req.body; // Destructuring the request body
    
    try {
        // Check if the user already has settings
        let Settings = await settings.findOne({ userId });

        if (!Settings) {
            Settings = new settings({
                userId,
                Bookingsettings: {
                    textColor: textColor || '#000000', 
                    backgroundColor: backgroundColor || '#FFFFFF',  
                    backgroundImageUrl: backgroundImageUrl || '', 
                    description: description || '',  
                    buttonColor: buttonColor || '#FFFFFF',  
            }
            });
        } else {
            
            if (textColor) Settings.Bookingsettings.textColor = textColor;
            if (backgroundColor) Settings.Bookingsettings.backgroundColor = backgroundColor;
            if (backgroundImageUrl) Settings.Bookingsettings.backgroundImageUrl = backgroundImageUrl;
            if (description) Settings.Bookingsettings.description = description;
            if (buttonColor) Settings.Bookingsettings.buttonColor = buttonColor;
        }

        // Save the updated settings document
        const updatedSettings = await Settings.save();

        return res.status(200).json({
            success: true,
            message: 'Booking settings updated successfully',
            settings: updatedSettings, // Returning the updated settings
        });
    } catch (error) {
        console.error('Error updating booking settings:', error);
        next(error);
        return res.status(500).json({
            success: false,
            error: 'Internal server error',
        });
    }
};
export const getUserSettings = async (req: any, res: Response, next: NextFunction): Promise<Response | void> => {
    const userId = req.user?._id;

    try {
        const userSettings = await settings.findOne({ userId });

        if (!userSettings) {
            return res.status(404).json({
                success: false,
                message: 'Settings not found for this user',
            });
        }

        return res.status(200).json({
            success: true,
            settings: userSettings,
        });
    } catch (error) {
        console.error('Error fetching user settings:', error);
        next(error); 
        return res.status(500).json({
            success: false,
            error: 'Internal server error',
        });
    }
};