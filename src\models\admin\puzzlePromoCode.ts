import mongoose, { Schema, Document } from "mongoose";

//   interface Validity {
//     length: number | null; 
//     unit: 'days' | 'weeks' | 'months' | 'years' | 'indefinitely';
//   }


  
//   interface PriceChange {
//     status: boolean; // If price changes after validity
//     price: number | null; // Null if no price change
//   }
interface Discount {
  type: 'fixed' | 'percentage';
  amount: number;
}

  
  export interface IPUZZLEPromoCode extends Document {
    code: string;
    type: 'subscription' | 'credit_purchase' 
    discount: Discount;
    description: string;  
    expiringDate: Date;
    isActive: boolean;
    maxUsage: number;
    createdAt: Date;
    updatedAt: Date;

    // validity: Validity;
    // price_change_after_validity: PriceChange;
   
  }

  const discountSchema = new Schema<Discount>({
    type: { type: String, enum: ["fixed", "percentage"], required: true },
    amount: { type: Number, required: true },
  });
  
  const puzzlePromoCodeSchema = new Schema<IPUZZLEPromoCode>({
    code: { type: String, required: true, unique: true },
    type: { type: String, enum: ["subscription", "credit_purchase"], required: true },
    discount: { type: discountSchema, required: true },
    isActive: { type: Boolean, required: true, default:true },
    maxUsage: { type: Number, required: true },
  },{timestamps: true});
  
  export default mongoose.model<IPUZZLEPromoCode>(
    "PuzzlePromoCode",
    puzzlePromoCodeSchema
  );