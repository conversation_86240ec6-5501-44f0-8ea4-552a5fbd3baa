import { Response,NextFunction } from 'express';
import blog from '../../models/admin/blogs/blog';
import tags from '../../models/admin/blogs/tags';
import mongoose from 'mongoose';

function isValidSlug(slug: string): boolean {
  return /^[a-z0-9-]+$/.test(slug);
}

export const createBlog = async (req:any, res: Response, next: NextFunction) : Promise<Response> => {
    // const userId = req?.user?._id;
    if(!req.file){
      return res.status(400).json({
        success: false,
        error: 'No file uploaded',
      });
    }
    const { title, shortDescription, longDescription, authorName, tags, timePosted } = req.body;
    try {
      const imageUrl = req.file?.path;
      const imagePhotoId = req.file?.filename || '';
        if (!title || !shortDescription || !longDescription || !authorName) {
            return res.status(400).json({ success:false, error: 'Please fill all required fields!' });
          
        }
        let parsedTags: string[] = [];
        try {
          parsedTags = tags ? JSON.parse(tags) : [];
        } catch (parseError) {
          return res.status(400).json({success:false, error: 'Invalid tags format. Must be JSON array.' });
        }
        const newPost = new blog({
            title:title,
            shortDescription,
            longDescription,
            authorName,
            tags: parsedTags,
            timePosted: timePosted || new Date(),
            imageUrl,
            imagePublicId:imagePhotoId,
            hasImage:imageUrl ? true : false,
        });
      
      await newPost.save();
      return res.status(200).json({ success: true,message: 'Post created successfully!', post: newPost });
    } catch (error) {
      console.error('Error creating blog:', error);
      next(error);
      return res.status(500).json({ success: false, error: 'Server error' });
    }
}

export const getBlogs = async (req:any, res: Response, next: NextFunction) : Promise<Response> => {
    const userId = req?.user?._id;
    try {
        const blogPosts = await blog.find();
        return res.status(200).json({ success: true, blogPosts });
    } catch (error) {
        console.error('Error getting blogs:', error);
      next(error);
      return res.status(500).json({ success: false, error: 'Server error' });
    }
}

export const getBlogbyId = async (req: any, res: Response, next: NextFunction): Promise<Response> => {
    const { Id } = req.params;
    if (!isValidSlug(Id)) {
        return res.status(400).json({ success: false, error: 'Invalid blog ID format.' });
    }
    try {
        let blogPost = await blog.findOne({ uniqueKey: Id });
        if (!blogPost) {
            blogPost = await blog.findOne({ previousSlugs: Id });
            if (blogPost) {
                return res.status(301).json({
                    success: false,
                    redirect: `${blogPost.uniqueKey}`,
                    message: 'This blog has moved. Redirecting...',
                });
            } else {
                return res.status(404).json({ success: false, error: 'Blog post not found' });
            }
        }
        return res.status(200).json({ success: true, blogPost });
    } catch (error) {
        console.error('Error getting blog:', error);
        next(error);
        return res.status(500).json({ success: false, error: 'Server error' });
    }
}

export const updateBlog = async (req: any, res: Response, next: NextFunction): Promise<Response> => {
    const { Id } = req.params;
    if (!isValidSlug(Id)) {
        return res.status(400).json({ success: false, error: 'Invalid blog ID format.' });
    }
    const { title, shortDescription, longDescription, authorName, tags } = req.body;
    const imageUrl = req.file?.path || '';
    const imagePhotoId = req.file?.filename || '';

    if (!title || !shortDescription || !longDescription || !authorName) {
        return res.status(400).json({ success: false, error: 'Please fill all required fields!' });
    }

    let parsedTags: string[] = [];
    try {
        parsedTags = tags ? JSON.parse(tags) : [];
    } catch {
        return res.status(400).json({ success: false, error: 'Invalid tags format. Must be JSON array.' });
    }

    const uniqueKey = title
        .replace(/[^a-z0-9\s-]/g, '')
        .replace(/\s+/g, '-')
        .trim();

    try {
        const existing = await blog.findOne({ title: title.toLowerCase(), uniqueKey: { $ne: Id } });
        if (existing) {
            return res.status(400).json({ success: false, error: 'A blog post with this title already exists.' });
        }

        const blogPost = await blog.findOne({ uniqueKey: Id });
        if (!blogPost) {
            return res.status(404).json({ success: false, error: 'Blog post not found' });
        }

        // If the slug is changing, store the old one
        if (blogPost.uniqueKey !== uniqueKey) {
            blogPost.previousSlugs = blogPost.previousSlugs || [];
            if (!blogPost.previousSlugs.includes(blogPost.uniqueKey)) {
                blogPost.previousSlugs.push(blogPost.uniqueKey);
            }
        }

        blogPost.title = title;
        blogPost.uniqueKey = uniqueKey;
        blogPost.shortDescription = shortDescription;
        blogPost.longDescription = longDescription;
        blogPost.authorName = authorName;
        blogPost.tags = parsedTags;
        blogPost.imageUrl = imageUrl;
        blogPost.imagePublicId = imagePhotoId;
        blogPost.hasImage = Boolean(imageUrl);

        await blogPost.save();

        return res.status(200).json({ success: true, message: 'Post updated successfully!', post: blogPost });
    } catch (error) {
        console.error('Error updating blog:', error);
        next(error);
        return res.status(500).json({ success: false, error: 'Server error' });
    }
};

export const deleteBlog = async (req: any, res: Response, next: NextFunction): Promise<Response> => {
    const { Id } = req.params;
    if (!isValidSlug(Id)) {
        return res.status(400).json({ success: false, error: 'Invalid blog ID format.' });
    }
    try {
        const deletedPost = await blog.findOneAndDelete({ uniqueKey: Id });
        if (!deletedPost) {
            return res.status(404).json({ success: false, error: 'Blog post not found' });
        }

        return res.status(200).json({ success: true, message: `Blog post with ID ${Id} deleted successfully!` });
    } catch (error) {
        console.error('Error deleting blog:', error);
        next(error); 
        return res.status(500).json({ success: false, error: 'Server error' });
    }
};


export const createTag = async (req: any, res: Response, next: NextFunction): Promise<Response | void> => {
    const { name } = req.body;
  
    try {
      const newTag = new tags({ name });
      await newTag.save();
  
      return res.status(201).json({ success: true, message: `Tag '${name}' created successfully!`, tag: newTag });
    } catch (error) {
      console.error('Error creating tag:', error);
  
      if ((error as mongoose.Error.ValidationError)) {
        return res.status(400).json({ success: false, error: 'Invalid data or duplicate tag name' });
      }
  
      next(error);
      return res.status(500).json({ success: false, error: 'Server error' });
    }
};
export const getTags = async (req: any, res: Response, next: NextFunction): Promise<Response | void> => {
    try {
      const tagsList = await tags.find();
      return res.status(200).json({ success: true, tagsList });
    } catch (error) {
      console.error('Error getting tags:', error);
      next(error);
      return res.status(500).json({ success: false, error: 'Server error' });
    }
};
  
export const deleteTag = async (req: any, res: Response, next: NextFunction): Promise<Response | void> => {
    const { Id } = req.params;
  
    try {
      const deletedTag = await tags.findByIdAndDelete(Id);
  
      if (!deletedTag) {
        return res.status(404).json({ success: false, error: 'Tag not found' });
      }
  
      return res.status(200).json({ success: true, message: `Tag '${deletedTag.name}' deleted successfully!` });
    } catch (error) {
      console.error('Error deleting tag:', error);
      next(error);
      return res.status(500).json({ success: false, error: 'Server error' });
    }
};