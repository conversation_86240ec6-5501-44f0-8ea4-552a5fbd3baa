import mongoose, { Schema, Document } from 'mongoose';

interface ISupportTicket extends Document {
  userId: mongoose.Types.ObjectId; 
  title: string;                  
  description: string;           
  status: 'open'| 'in-progress'| 'resolved'|'closed';                
  priority: 'low'|'medium'| 'high';             
  ip: string; 
  createdAt: Date;
  updatedAt: Date;
}

const SupportTicketSchema: Schema = new Schema(
  {
    userId: { type: Schema.Types.ObjectId, ref: 'User', required: true },
    title: { type: String, required: true },
    description: { type: String, required: true },
    status: {
      type: String,
      enum: ['open', 'in-progress', 'resolved', 'closed'],
      default: 'open',
    },
    priority: {
      type: String,
      enum: ['low', 'medium', 'high'],
      default: 'medium',
    },
    ip: { type: String, required: true },
  },
  { timestamps: true }
);

export default mongoose.model<ISupportTicket>('SupportTicket', SupportTicketSchema);
