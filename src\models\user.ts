import mongoose, { Document, Schema } from 'mongoose';
import bcrypt from 'bcrypt';
import calendarSettings, {ICalendarSettings} from './calendarSettings';
import settings from './settings';
import { createDefaultExternalAccounts } from '../controllers/externalAccount';
import digitalProfile from './digitalProfile';
// import externalAccount from './externalAccount';
interface ILeadSettings {
  service: string;
  leadLocation?: string[];
}
const LeadSettingsSchema: Schema<ILeadSettings> = new Schema({
  service: { type: String, required: true, default:'service' },
  leadLocation: { type: [String], default:[]},
});
export interface IUser extends Document {
  first_name: string;
  last_name: string;
  username: string;
  email: string;
  role:string;
  password: string;
  stripesConsumerId:string;
  stripesAccountId:string;
  userSubscription?:mongoose.Types.ObjectId;
  user_photo?: string;
  hasProfileImage: boolean;
  user_photo_PublicId:string;
  phone?:string;
  state?:string;
  country?:string;
  business_bio?:string;
  timeFormat?:string;
  dateFormat?:string;
  isEmailVerified: boolean;
  emailConfirmationToken?: string;
  emailConfirmationTokenExpires?: Date;
  resetPasswordToken?: string; 
  resetPasswordExpires?: Date; 
  createdAt: Date;
  updatedAt: Date;
  bookingLink?: string;
  business_name:string
  planId: string
  //Leads Profile
  leadSettings: ILeadSettings;
  totalCredit: number;
  isDeactivated:boolean;
  isDeleted:boolean;
  // location: {
  //   type: 'Point';
  //   coordinates: [number, number];
  //   locationSet: boolean;
  // };    totalLeads: number;
  comparePassword(candidatePassword: string): Promise<boolean>;
}

const userSchema: Schema<IUser> = new Schema({
  first_name: { type: String, required: true },
  last_name: { type: String, required: true },
  role: { type: String, default: 'user', enum: ['user', 'admin', 'superadmin'], required: true },
  stripesConsumerId: { type: String,
    required: function(this: IUser) {
    return this.role === 'user';
    }
  },
  stripesAccountId: { type: String},
  planId: { type: String, required: function(this: IUser) {
    return this.role === 'user';
    }},
  userSubscription: {type:mongoose.Types.ObjectId},
  username: { type: String, required: true, unique: true },
  email: { type: String, required: true, unique: true },
  password: { type: String, required: true },
  user_photo: { type: String },
  user_photo_PublicId: { type: String },
  hasProfileImage: { type: Boolean, default: false },
  state: { type: String },
  phone: { type: String },
  business_bio: { type: String },
  country: { type: String },
  isEmailVerified: { type: Boolean, default: false },
  emailConfirmationToken: { type: String },
  emailConfirmationTokenExpires: { type: Date },
  resetPasswordToken: { type: String },
  resetPasswordExpires: { type: Date },
  bookingLink: { type: String },
  //Lead PRofile
  business_name: { type: String, default:"busniness_name" },
  leadSettings: { type: LeadSettingsSchema, default: { service: 'service', leadLocation: [] } },
  totalCredit: { type: Number, required: true, default:0},
  isDeactivated: { type: Boolean, required: true, default:false},
  isDeleted: { type: Boolean, required: true, default:false},
  

  // location: {
  //   type: {
  //     type: String,
  //     enum: ['Point'],
  //     required: true,
  //   },
  //   coordinates: {
  //     type: [Number],
  //     required: true,
  //   },
  //   locationSet: { type: Boolean, required: true },
  // },
}, { timestamps: true });

userSchema.pre<IUser>('save', async function (next) {
  const user = this;
  if (!user.isModified('password')) {
    return next();
  }
  try {
    const salt = await bcrypt.genSalt(10);
    user.password = await bcrypt.hash(user.password, salt);
    next();
  } catch (error) {
    next(error as mongoose.CallbackError);
  }
});

// Method to compare passwords
userSchema.methods.comparePassword = async function (
  candidatePassword: string
): Promise<boolean> {
  return await bcrypt.compare(candidatePassword, this.password);
};
userSchema.pre<IUser>('save', async function (next) {
  const user = this as IUser;
  if (user.email) {
    user.email = user.email.toLowerCase();
  }
  if (user.username) {
    user.username = user.username.toLowerCase();
  }
  if (user.isNew) {
    const session = this.$session();
    try {
      const defaultCalendarSettings: Partial<ICalendarSettings> = {
        timeslots: '30',
        workdays: [
          { day: 'monday', startTime: '09:00', closingTime: '17:00' },
          { day: 'tuesday', startTime: '09:00', closingTime: '17:00' },
          { day: 'wednesday', startTime: '09:00', closingTime: '17:00' },
          { day: 'thursday', startTime: '09:00', closingTime: '17:00' },
          { day: 'friday', startTime: '09:00', closingTime: '17:00' }
        ],
        userId: user._id as mongoose.Types.ObjectId, 
        timezone: 'Europe/London'
      };
      const defaultSettings = new settings({
        userId: user._id,
        Bookingsettings: {
            buttonColor: '#FFFFFF',
            textColor: '#000000',
            backgroundColor: '#FFFFFF',
            backgroundImageUrl: '',
            description: 'Welcome to your booking page!',
        },
      });
      
      const defaultProfile = new digitalProfile({
        userId: user._id,
        websiteLinks: [],
        imageUrl: null,
        profileUrl:`${process.env.FRONTENDURL}/digital-profile/${user.username}`,
        details: {
          title: user.username, 
          bio: 'Welcome to my digital profile!',
        },
        styles: {
          // the blaze gradient is default
          backgroundColor: "linear-gradient(45deg, #B6E3C4, #FBD457)",
        },
      });
      if (session) {
        await defaultProfile.save({ session });
        await createDefaultExternalAccounts(user?._id as string, session);
        await defaultSettings.save({ session });
        await new calendarSettings(defaultCalendarSettings).save({ session });
      } else {
        console.warn('No session provided for related document creation');
      }
    } catch (error) {
      return next(error as mongoose.CallbackError);
    }
  }
  next();
});

const User = mongoose.model<IUser>('User', userSchema);
export default User;
