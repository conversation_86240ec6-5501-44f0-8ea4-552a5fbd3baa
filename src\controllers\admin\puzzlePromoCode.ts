import { Request, Response ,NextFunction} from "express";
import puzzlePromoCode from "../../models/admin/puzzlePromoCode";

export const applyPromoCode = async (
  code: string,
  amount: number
): Promise<{ promoPrice: number; discountAmount: number }> => {
  try {
    const promoCode = await puzzlePromoCode.findOne({ code, isActive: true });
    if (!promoCode || new Date() > promoCode.expiringDate) {
      throw new Error("Promo code is invalid or expired.");
    }

    const { type, amount: discountAmount } = promoCode.discount;
    let promoPrice = amount;

    if (type === "percentage") {
      promoPrice = amount - (amount * discountAmount) / 100;
    } else if (type === "fixed") {
      promoPrice = amount - discountAmount;
    }

    promoPrice = Math.max(promoPrice, 0);

    return { promoPrice, discountAmount };
  } catch (error) {
    console.error("Error applying promo code:", error);
    throw error;
  }
};



export const createPromoCode = async (req: Request, res: Response , next:NextFunction):Promise<Response> =>{
  try {
    const { code,codeType,discountType, discountAmount, maxUsage} = req.body;
    const promoCode = new puzzlePromoCode({
        code,
        type:codeType,
        discount:{
            type:discountType,
            amount:discountAmount
        },
        maxUsage
    });
    await promoCode.save();
    return res.status(201).json({ message: "Promo code created successfully", promoCode });
  } catch (error) {
        next(error)
        console.error('Error creating code:', error);
        return res.status(500).json({ error: 'Failed to create promo code' });
  }
};

export const getPromoCodes = async (req: Request, res: Response , next:NextFunction):Promise<Response> => {
  try {
    const promoCodes = await puzzlePromoCode.find();
    return res.status(200).json(promoCodes);
  } catch (error) {
    next(error)
    console.error('Error fetching codes:', error);
    return res.status(500).json({ error: 'Failed to fetch promo codes' });
  }
};

export const getPromoCodeById = async (req: Request, res: Response, next:NextFunction):Promise<Response> => {
  try {
    const {Id}= req.params;
    const promoCode = await puzzlePromoCode.findById(Id);
    if (!promoCode) return res.status(404).json({ error: "Promo code not found" });
    return res.status(200).json(promoCode);
  } catch (error) {
    next(error)
    console.error('Error fetching code:', error);
        return res.status(500).json({ error: 'Failed to fetch promo code' });
  }
};

export const updatePromoCode = async (req: Request, res: Response , next:NextFunction):Promise<Response> => {
  try {
    const {Id}= req.params;
    const { code,codeType,discountType, discountAmount, maxUsage, isActive} = req.body;
    const promoCode = await puzzlePromoCode.findByIdAndUpdate(
        Id,
        {
        code,
        type:codeType,
        discount:{                  
            type:discountType,
            amount:discountAmount
        },
        maxUsage,
        isActive
    }, {
      new: true,
    });
    if (!promoCode) return res.status(404).json({ error: "Promo code not found" });
    return res.status(200).json({ message: "Promo code updated successfully", promoCode });
  } catch (error) {
    next(error)
    console.error('Error updating code:', error);
    return res.status(500).json({ error: 'Failed to update promo code' });
  }
};

export const deletePromoCode = async (req: Request, res: Response , next:NextFunction):Promise<Response> => {
  try {
    const {Id}= req.params;
    const promoCode = await puzzlePromoCode.findByIdAndDelete(Id);
    if (!promoCode) return res.status(404).json({ error: "Promo code not found" });
    return res.status(200).json({ message: "Promo code deleted successfully" });
  } catch (error) {
    next(error)
    console.error('Error deleting code:', error);
    return res.status(500).json({ error: 'Failed to delete promo code' });
  }
};
