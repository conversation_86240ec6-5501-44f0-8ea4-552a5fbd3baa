import BlockedTime from '../../models/blockTime';

const deleteInvalidBlockedTimes = async () => {
    const now = new Date();
    try {
        // Delete any blocked time entries that have ended (i.e., endTime is in the past)
        const result = await BlockedTime.deleteMany({
            endTime: { $lt: now },
        });
        console.log(`${result.deletedCount} invalid blocked time(s) deleted.`);
    } catch (error) {
        console.error('Error deleting invalid blocked times:', error);
    }
};

// // Function to delete invalid/expired blocked times
// const deleteInvalidBlockedTimes = async () => {
//     const now = new Date();
//     try {
//         const result = await CalendarSettings.updateMany(
//             {}, // Find all calendar settings
//             { $pull: { calendarBlockedTimes: { endTime: { $lt: now } } } } // Remove any blocked times that have expired
//         );
//         console.log(`Expired blocked times deleted from ${result.nModified} records.`);
//     } catch (error) {
//         console.error('Error deleting expired blocked times:', error);
//     }
// };

// Schedule the deletion function to run every 12 hours
// /cron.schedule('0 */12 * * *', deleteInvalidBlockedTimes);
