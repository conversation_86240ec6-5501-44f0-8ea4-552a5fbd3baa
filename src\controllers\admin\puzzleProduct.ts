import { Request,Response } from "express";
import { createStripeProduct } from "../../services/stripesService";
import PuzzleProduct from "../../models/admin/puzzleProduct";

export const createPuzzleProduct = async (req: Request, res: Response):Promise<Response> => {
    try {
      const { name, description, active, features } = req.body;
  
      const stripeProduct = await createStripeProduct(name, description)
  
      const newProduct = new PuzzleProduct({
        stripeProductId: stripeProduct.id,
        name,
        description,
        active,
        features,
      });
  
      const savedProduct = await newProduct.save();
  
     return res.status(201).json({
        message: 'Product created successfully!',
        product: savedProduct,
      });
    } catch (err) {
      console.error('Error creating product:', err);
      return res.status(500).json({ error: 'Failed to create product', err });
    }
  };
export const getAllPuzzleProducts = async (req: Request, res: Response):Promise<Response> => {
  try {
    const products = await PuzzleProduct.find()
    return res.status(200).json({
      message: 'Products fetched successfully!',
      products: products,
    });
  } catch (err) {
    console.error('Error getting all product:', err);
    return res.status(500).json({ error: 'Failed to create product', err });
  }
}

export const getPuzzleProductById = async (req: Request, res: Response):Promise<Response> => {
  try {
    const {Id} = req.params
    const product = await PuzzleProduct.findById(Id)
    return res.status(200).json({
      message: 'Product fetched successfully!',
      product,
    });
  } catch (err) {
    console.error('Error getting all product:', err);
    return res.status(500).json({ error: 'Failed to create product', err });
  }
}

export const updatePuzzleProduct = async (req: Request, res: Response):Promise<Response> => {
  try {
    const { name, description, active, features } = req.body;

    const {Id} = req.params
    const product = await PuzzleProduct.findByIdAndUpdate(Id, 
      {
        name,
        description,
        active,
        features
      }, {new:true})
    return res.status(200).json({
      message: 'Product fetched successfully!',
      product,
    });
  } catch (err) {
    console.error('Error getting all product:', err);
    return res.status(500).json({ error: 'Failed to create product', err });
  }
}

export const deletePuzzleProduct = async (req: Request, res: Response):Promise<Response> => {
  try {
    const {Id} = req.params
    await PuzzleProduct.findByIdAndDelete(Id)
    return res.status(200).json({
      message: 'Product deleted successfully!'
    });
  } catch (err) {
    console.error('Error getting all product:', err);
    return res.status(500).json({ error: 'Failed to create product', err });
  }
}