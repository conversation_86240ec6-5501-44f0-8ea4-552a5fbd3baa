import mongoose, { Schema, Document } from 'mongoose';

interface IPurchaseLead extends Document {
    user: mongoose.Types.ObjectId;
    leads: mongoose.Types.ObjectId[];
}

const leadPurchaseSchema: Schema<IPurchaseLead> = new Schema({
    user: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
    },
    leads: [{
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Business_Lead'
    }]
},{ timestamps: true });

export default mongoose.model<IPurchaseLead>('PurchaseLead', leadPurchaseSchema);
