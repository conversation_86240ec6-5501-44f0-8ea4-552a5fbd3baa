import { Server } from 'socket.io';
import { Socket } from 'socket.io';
import SocketUser from '../models/socket';

let io: Server | undefined;

/**
 * Initializes the socket service.
 * @param ioInstance - The Socket.IO instance.
 */
export const socketService = (ioInstance: Server) => {
  io = ioInstance;

  io.on('connection', (socket: Socket) => {
    console.log('New client connected', socket.id);

    // Handle custom events here
    socket.on('subscribe', async (userId: string) => {
      socket.join(userId);
      console.log(`User ${userId}, Socket ${socket.id} joined room ${userId}`);

      // Save the socket ID to the database
      await SocketUser.create({ userId, socketId: socket.id });
    });

    socket.on('unsubscribe', (room: string) => {
      socket.leave(room);
      console.log(`Socket ${socket.id} left room ${room}`);
    });

    socket.on('disconnect', async () => {
      console.log('Client disconnected', socket.id);

      // Remove the socket ID from the database
      await SocketUser.findOneAndDelete({ socketId: socket.id });
    });
  });
};

/**
 * Emit a socket event to a specific user.
 * @param event - The name of the event to emit.
 * @param userId - The ID of the user to whom the event should be sent.
 * @param data - The data to send with the event.
 */
export const emitToUser = async (event: string, userId: string, data: any) => {
  if (io) {
    try {
      const socketUser = await SocketUser.findOne({ userId });
      if (socketUser) {
        console.log(socketUser.socketId);
        io.to(socketUser.socketId).emit(event, data);
        console.log(`Event ${event} sent to user ${userId}`);
      } else {
        console.log(`No socket found for user ${userId}`);
      }
    } catch (error) {
      console.error(`Error emitting event to user ${userId}:`, error);
    }
  }
};

/**
 * Emit a socket event to all connected clients.
 * @param event - The name of the event to emit.
 * @param data - The data to send with the event.
 */
export const emitEvent = (event: string, data: any) => {
  if (io) {
    io.emit(event, data);
  }
};
