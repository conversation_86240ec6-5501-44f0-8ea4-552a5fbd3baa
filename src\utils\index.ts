import mongoose, { isObjectIdOrHexString } from "mongoose";

/**
 * @dev omits specified properties and returns remnant
 */
export const omit = <T extends object, K extends keyof T>(
  obj: T,
  ...keys: K[]
): Omit<T, K> => {
  const result = { ...obj };
  keys.forEach((key) => delete result[key]);
  return result;
};

export const pick = <O extends Record<string, any>, K extends keyof O>(
  obj: O,
  ...keys: K[]
) => {
  const emptyObj = {} as Record<K, any>;
  keys.forEach((k) => (emptyObj[k] = obj[k]));
  return emptyObj as Pick<O, K>;
};

export function objectKeys<T extends Record<string, any>>(obj: T) {
  return Object.keys(obj) as Array<keyof T>
}

type CaseArray<C, R> = [C, R];

/**
 * @dev take this as an inline switch
 */
export function inlineSwitch<Case extends any, Return extends any>(
  check: any,
  ...caseArrays: Array<CaseArray<Case, Return>>
) {
  let pickedReturn: Return | undefined = undefined;
  for (let i = 0; i < caseArrays.length; i++) {
    const caseArray = caseArrays[i];
    if (caseArray[0] === check) {
      pickedReturn = caseArray[1];
      break;
    }
  }
  return pickedReturn;
};

export function isPositive(num: number) {
  return num >= 0;
}

/**
 * @dev helps check if an unwanted value is in a list of values with an array
 * @default [undefined,null,'']
 * @replaces {if(!first_name || !last_name || !username || !email || !password)}
 */
export function isUnwantedInArray(values: any[], unwantedValues = [undefined, null, '']) {
  return isPositive(
    unwantedValues.findIndex(unwantedValue => values.includes(unwantedValue))
  )
}

/**
 * @dev returns the objectId as string from mongoose.ObjectId
 */
export function getObjectIdString(objectId: unknown) {
  return typeof objectId === 'object' ? (objectId as any).toString() : ''
}