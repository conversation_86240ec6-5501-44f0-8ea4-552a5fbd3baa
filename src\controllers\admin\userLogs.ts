import { Response, NextFunction } from "express";
import User from "../../models/user";
import userLogs from "../../models/admin/userLogs";

export const getUserswithLogs = async (
  req: any,
  res: Response,
  next: NextFunction
): Promise<Response> => {
  try {
    const usersWithLogs = await User.aggregate([
      {
        $lookup: {
          from: "userlogs",
          localField: "_id",
          foreignField: "userId",
          as: "logs",
        },
      },
      {
        $project: {
          name: 1,
          email: 1,
          logCount: { $size: "$logs" },
        },
      },
    ]);

    return res.status(200).json({
      logs: usersWithLogs,
    });
  } catch (error) {
    next(error);
    console.error("Error fetching users with logs:", error);
    return res.status(500).json({ error: "Failed to fetch users with logs" });
  }
};

export const createUserLogs = async (
  req: any,
  res: Response,
  next: NextFunction
): Promise<Response> => {
  const { type, description, ip, user_agent } = req.body;
  const userId = req.user?._id;
  try {
    if (userId) {
      await userLogs.create({
        type,
        description,
        ip,
        user_agent,
        userId,
      });
    }
    return res.status(201).json({
      success: true,
      message: "User log recorded successfully",
    });
  } catch (error) {
    next(error);
    console.error("Error creating users with logs:", error);
    return res
      .status(500)
      .json({ message: "Failed to create users with logs" });
  }
};

export const getUserLogs = async (
  req: any,
  res: Response,
  next: NextFunction
): Promise<Response> => {
  try {
    const { userId } = req.params;
    const { type, limit = 10, page = 1 } = req.query;

    const query: any = { userId };

    if (type) {
      query.type = type;
    }

    const logs = await userLogs
      .find(query)
      .sort({ timestamp: -1 })
      .skip((Number(page) - 1) * Number(limit))
      .limit(Number(limit));

    return res.status(200).json({
      logs: logs
    });
  } catch (error) {
    next(error);
    console.error("Error creating user logs:", error);
    return res.status(500).json({ error: "Failed to get user logs" });
  }
};

// export const logAction = (type: string, description: string) => {
//   return async (req: Request, res: Response, next: NextFunction) => {
//     try {
//       const ip = req.headers["x-forwarded-for"] || req.socket.remoteAddress || "Unknown";
//       const user_agent = req.headers["user-agent"] || "Unknown";

//       // Assuming user ID is stored in req.user after authentication middleware
//       const userId = req.user?._id;

//       if (userId) {
//         await UserLog.create({
//           type,
//           description,
//           ip,
//           user_agent,
//           userId,
//         });
//       }

//       next();
//     } catch (error) {
//       console.error("Error logging action:", error);
//       next(); // Allow the request to proceed even if logging fails
//     }
//   };
// };
// router.post(
//     "/example-endpoint",
//     authenticateUser,
//     logAction("example", "User performed an example action"),
//     (req, res) => {
//       res.status(200).json({ message: "Action logged successfully" });
//     }
//   );
