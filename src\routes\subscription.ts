import { Router } from 'express';
import { authMiddleware } from '../middleware/authService';
import { SetupIntent, AddPaymentMethod, GetSubscriptionPlans,AddPaymentMethodOnSignup, upgradeSubscription, GetCurrentSubscriptionPlan} from '../controllers/subscriptions';
const router: Router = Router();


router.get('/subscription-plans',GetSubscriptionPlans);
router.get('/user-subscription-plan',authMiddleware, GetCurrentSubscriptionPlan);
router.get('/setup-intent',authMiddleware,SetupIntent);
router.post('/add-payment-method',authMiddleware,AddPaymentMethod);
router.post('/add-payment/on-signup',AddPaymentMethodOnSignup);

router.put('/upgrade-subscription',authMiddleware,upgradeSubscription);
export default router;