/**
 * Converts a price to the smallest currency unit for <PERSON><PERSON>'s amount field.
 * @param price - The price in decimal format (e.g., 10.99).
 * @param currency - The currency code (e.g., 'usd', 'jpy'). Defaults to 'usd'.
 * @returns The price in the smallest currency unit (e.g., 1099 for USD, 1100 for jpy).
 */
export function convertToMinorUnits(price: number, currency: string = 'usd'): number {
    const zeroDecimalCurrencies = ['jpy', 'krw', 'vnd']; 
    if (zeroDecimalCurrencies.includes(currency.toUpperCase())) {
      return Math.round(price); // No conversion needed, as these don't use decimals.
    }
    return Math.round(price * 100); // For currencies with cents.
  }

/**
 * Calculates the total fee (£1.00 + 2%) in cents for a given price in cents.
 * @param priceInCents - The original price in cents (e.g., 5000 for £50.00)
 * @returns - The total fee in cents
 */
export function calculateFeeInCents(priceInCents: number): number {
  const fixedFeeInCents = 100;            // £1.00 in cents
  const percentageFee = 0.02;             // 2% fee

  // Calculate the percentage fee in cents
  const percentageFeeInCents = Math.round(priceInCents * percentageFee);

  // Calculate the total fee
  const totalFeeInCents = fixedFeeInCents + percentageFeeInCents;

  return totalFeeInCents;
}