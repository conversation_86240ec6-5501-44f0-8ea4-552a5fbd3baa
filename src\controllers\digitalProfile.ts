import { NextFunction, Response } from "express";
import digitalProfile from "../models/digitalProfile";
import User from "../models/user";
import { cloudinary } from "../utils/cloudinary";

export const addWebsiteLink = async (
  req: any,
  res: Response,
  next: NextFunction
): Promise<Response> => {
  const userId = req.user?._id;
  const { title, url, icon, isActive, buttonStyle } = req.body;
  try {
    if (!title || !url || !buttonStyle?.id) {
      return res
        .status(400)
        .json({ error: "Title, URL, and button style ID are required" });
    }

    let profile = await digitalProfile.findOne({ userId });

    if (!profile) {
      profile = await digitalProfile.create({
        userId,
        websiteLinks: [],
        imageUrl: null,
        details: { title: "", bio: "" },
        styles: { backgroundColor: "#FFFFFF" },
      });
    }

    const duplicateLink = profile.websiteLinks.find(
      (link) => link.title === title
    );

    if (duplicateLink) {
      return res.status(400).json({
        success: false,
        error: `A website link with the title "${title}" already exists.`,
      });
    }
    const newWebsiteLink = {
      title,
      url,
      icon: icon || null,
      isActive: isActive ?? true,
      buttonStyle: { id: buttonStyle.id },
    };

    profile.websiteLinks.push(newWebsiteLink);
    await profile.save();

    return res.status(200).json({
      success: true,
      message: "Website link added successfully",
      profile,
    });
  } catch (error) {
    console.error("Error adding website link:", error);
    next(error);
    return res.status(500).json({ success: false, error: "Server error" });
  }
};

export const getDigitalProfileByUsername = async (
  req: any,
  res: Response,
  next: NextFunction
) => {
  const { username } = req.params;

  try {
    // Find the user by username
    const user = await User.findOne({ username });
    if (!user) {
      return res.status(404).json({ success: false, error: "User not found" });
    }

    const DigitalProfile = await digitalProfile
      .findOne({ userId: user._id })
      .populate("userId", "username imageUrl");
    if (!DigitalProfile) {
      return res
        .status(404)
        .json({ success: false, error: "Profile not found" });
    }

    return res.status(200).json({ success: true, profile: DigitalProfile });
  } catch (error) {
    console.error("Error fetching website links:", error);
    return res.status(500).json({ success: false, error: "Server error" });
  }
};

export const updateWebsiteLinkSettings = async (
  req: any,
  res: Response,
  next: NextFunction
): Promise<Response> => {
  const userId = req.user?._id;
  const { title } = req.params;
  const { newTitle, url, icon, isActive, buttonStyle } = req.body;
  try {
    if (
      !title ||
      (!newTitle && !url && !icon && isActive === undefined && !buttonStyle?.id)
    ) {
      return res.status(400).json({
        success: false,
        error:
          "Provide at least one field to update (newTitle, url, icon, isActive, or buttonStyle)",
      });
    }
    const profile = await digitalProfile.findOne({ userId });
    if (!profile) {
      return res
        .status(404)
        .json({
          success: false,
          error: "Digital profile not found for this user.",
        });
    }

    const websiteLink = profile.websiteLinks.find(
      (link) => link.title === title
    );
    if (!websiteLink) {
      return res
        .status(404)
        .json({
          success: false,
          error: `Website link with title "${title}" not found.`,
        });
    }

    if (newTitle && newTitle !== title) {
      const duplicateLink = profile.websiteLinks.find(
        (link) => link.title === newTitle
      );
      if (duplicateLink) {
        return res.status(400).json({
          success: false,
          error: `A website link with the title "${newTitle}" already exists.`,
        });
      }
      websiteLink.title = newTitle;
    }
    if (url) websiteLink.url = url;
    if ([undefined, null].includes(icon) || (typeof icon === 'string')) websiteLink.icon = icon;
    if (isActive !== undefined) websiteLink.isActive = isActive;
    if (buttonStyle?.id) websiteLink.buttonStyle.id = buttonStyle.id;

    await profile.save();
    return res.status(200).json({
      success: true,
      message: "Website link updated successfully.",
      profile,
    });
  } catch (error) {
    console.error("Error updating website link:", error);
    next(error);
    return res.status(500).json({ success: false, error: "Server error" });
  }
};

export const getDigitalProfile = async (
  req: any,
  res: Response,
  next: NextFunction
): Promise<Response> => {
  const userId = req.user?._id;
  const username = req.user?.username;
  try {
    let profile = await digitalProfile.findOne({ userId });

    if (!profile) {
      const profileUrl = `${process.env.FRONTENDURL}/links/${username}`;
      profile = new digitalProfile({
        userId,
        profileUrl,
        websiteLinks: [],
        imageUrl: null,
        details: {
          title: "My digital profile",
          bio: "Welcome to my digital profile!",
        },
        styles: {
          // this is the blaze gradient, you can change it to any other default gradient
          backgroundColor: `linear-gradient(45deg, #B6E3C4, #FBD457)`,
        },
      });

      await profile.save();
    }

    return res.status(200).json({
      success: true,
      profile,
    });
  } catch (error) {
    console.error("Error fetching or creating digital profile:", error);
    next(error);
    return res
      .status(500)
      .json({ success: false, error: "Internal Server error" });
  }
};

export const removeWebsiteLink = async (
  req: any,
  res: Response,
  next: NextFunction
): Promise<Response> => {
  const userId = req.user?._id;
  const { title } = req.params;

  try {
    const profile = await digitalProfile.findOne({ userId });

    if (!profile) {
      return res
        .status(404)
        .json({ success: false, error: "Digital profile not found" });
    }

    const websiteIndex = profile.websiteLinks.findIndex(
      (link) => link.title === title
    );

    if (websiteIndex === -1) {
      return res
        .status(404)
        .json({ success: false, error: "Website link not found" });
    }

    profile.websiteLinks.splice(websiteIndex, 1);

    await profile.save();

    return res.status(200).json({ success: true, profile });
  } catch (error) {
    console.error("Error removing website link:", error);
    next(error);
    return res.status(500).json({ success: false, error: "Server error" });
  }
};

export const updateDigitalProfile = async (
  req: any,
  res: Response,
  next: NextFunction
) => {
  const userId = req.user?._id;
  const { title, bio, backgroundColor } = req.body;
  if (!title || !bio || !backgroundColor) {
    return res.status(400).json({
      success: false,
      error:
        "Provide at least one field to update (backgroundColor, bio,title)",
    });
  }
  try {
    const profile = await digitalProfile.findOne({ userId });

    if (!profile) {
      return res
        .status(404)
        .json({ success: false, error: "Digital profile not found" });
    }

    profile.details = {
      title: title || profile.details.title,
      bio: bio || profile.details.bio,
    };

    profile.styles = {
      backgroundColor: backgroundColor || profile.styles.backgroundColor,
    };

    await profile.save();

    return res.status(200).json({ success: true, profile });
  } catch (error) {
    console.error("Error updating digital profile:", error);
    next(error);
    return res.status(500).json({ success: false, error: "Server error" });
  }
};

export const uploadDigitalProfileImage = async (
  req: any,
  res: Response,
  next: NextFunction
): Promise<Response> => {
  const userId = req?.user?._id;
  if (!req.file) {
    return res.status(400).json({
      success: false,
      error: "No file uploaded",
    });
  }
  try {
    const imageUrl = req.file?.path;
    const imagePhotoId = req.file?.filename || "";
    const profile = await digitalProfile.findOne({ userId });
    if (!profile) {
      return res
        .status(404)
        .json({ success: false, error: "Digital profile not found" });
    }
    const oldPublicId = profile.imagePublicId;
    if (oldPublicId) {
      await cloudinary.v2.uploader.destroy(oldPublicId);
    }
    profile.imageUrl = imageUrl;
    profile.hasImage = true;
    profile.imagePublicId = imagePhotoId;
    await profile.save();
    return res
      .status(200)
      .json({ success: true, message: "Profile image uploaded successfully" });
  } catch (error) {
    console.error("Error updating digital profile image:", error);
    next(error);
    return res.status(500).json({ success: false, error: "Server error" });
  }
};
