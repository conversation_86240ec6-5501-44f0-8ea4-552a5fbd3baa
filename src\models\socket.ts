import mongoose, { Schema, Document } from 'mongoose';
import { IUser } from './user';

export interface ISocketUser extends Document {
  userId: IUser['_id'];
  socketId: string;
}

const socketUserSchema: Schema = new Schema<ISocketUser>({
  userId: { type: Schema.Types.ObjectId, ref: 'User', required: true },
  socketId: { type: String, required: true },
});

const SocketUser = mongoose.model<ISocketUser>('SocketUser', socketUserSchema);
export default SocketUser;
