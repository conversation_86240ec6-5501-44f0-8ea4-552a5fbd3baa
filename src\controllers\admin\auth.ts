import { NextFunction, Request, Response } from "express";
import User from "../../models/user";

export const RegisterAdmin = async (
    req: Request,
    res: Response,
    next: NextFunction
  ): Promise<Response> => {
    const {
      first_name,
      last_name,
      username,
      email,
      password
    } = req.body;
  
    try {
      if (
        !first_name ||
        !last_name ||
        !username ||
        !email ||
        !password 
        
      ) {
        return res
          .status(400)
          .json({ success: false, error: "All fields are required" });
      }
      const userEmail = email.toLowerCase()
      const existingUser = await User.findOne({ $or: [{ email:userEmail }, { username }] });
      if (existingUser) {
        return res
          .status(400)
          .json({ error: "User with that email or username already exists" });
      }
      const newUser = new User({
        first_name,
        last_name,
        username,
        email:userEmail,
        password,
        role:"admin"
      });
  
      await newUser.save();
     //TODO: Send AN email with the login DETAILS TO THE ADMIN USER 

      return res.status(201).json({
        message: "Admin User registered successfully",
        userId: newUser._id,
      });
    } catch (error) {
      console.log(error);
      next(error);
      return res
        .status(500)
        .json({ success: false, error: "Internal Server Error" });
    }
  };