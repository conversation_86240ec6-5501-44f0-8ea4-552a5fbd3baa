import axios from 'axios';
import { NextFunction, Request, Response } from 'express';
import industries from '../models/industries';
import businessLeads from '../models/Leads/businessLeads';
import cachedLocation from '../models/Leads/cachedLocation';
import leadCart from '../models/Leads/leadCart';
import purchasedLead from '../models/Leads/purchasedLead';
import User from '../models/user';
import { Transaction } from '../models/wallet';
import { HiddenTextRenderer } from '../utils/hideText';

const getCoordinates = async (location: string): Promise<{ latitude: number; longitude: number } | null> => {
    const url = 'https://api.opencagedata.com/geocode/v1/json';
    const params = {
        q: location,
        key: process.env.OPENCAGE_API_KEY,
        limit: 1,
        countrycode: 'GB', // Restrict to the United Kingdom
    };

    try {
        const response = await axios.get(url, { params });
        if (response.status === 200 && response.data.results.length > 0) {
            const latitude = response.data.results[0].geometry.lat;
            const longitude = response.data.results[0].geometry.lng;
            return { latitude, longitude }; // Return the coordinates
        } else {
            console.log('No results found for the given location.');
            return null; // Return null if no results
        }
    } catch (error) {
        console.error('Error fetching coordinates:', error);
        return null; // Return null in case of an error
    }
};

const createOrUpdateLocation = async (location: string): Promise<{ longitude: number; latitude: number } | null> => {
    try {
        const existingLocation = await cachedLocation.findOne({ location });
        if (existingLocation) {
            return {
                longitude: existingLocation.longitude,
                latitude: existingLocation.latitude,
            };
        } else {
            const coordinates = await getCoordinates(location);
            if (coordinates) {
                const newLocation = new cachedLocation({
                    location:location.toLowerCase(),
                    longitude: coordinates.longitude,
                    latitude: coordinates.latitude,
                });
                await newLocation.save();
                return {
                    longitude: newLocation.longitude,
                    latitude: newLocation.latitude,
                };
            } else {
                console.log('Could not retrieve coordinates for the location.');
                return null; // Handle the case when coordinates are not found
            }
        }
    } catch (error) {
        console.error('Error creating or updating location:', error);
        return null; // Return null in case of an error
    }
};

export const searchBusinessesLeads = async (req: Request, res: Response, next: NextFunction): Promise<Response | void> => {
    const { keyword, location,page = 1, limit = 10  } = req.query;
    try {
        let businessQuery:any = {}
        const locationArray = typeof location === 'string' ? [location] : location;
        if (locationArray && Array.isArray(locationArray)) {
            const locationFilters = await Promise.all(
                locationArray.map(async (locat) => {
                    if (typeof locat === 'string') {
                        const coords = await createOrUpdateLocation(locat);
                        if (coords) {
                            return {
                                location: {
                                    $geoWithin: {
                                        $centerSphere: [
                                            [coords.longitude, coords.latitude],
                                            10 / 6378.1 
                                        ]
                                    }
                                }
                            };
                        }
                    }
                    return null;
                })
            );
            const validLocationFilters = locationFilters.filter(Boolean);
            if (validLocationFilters.length > 0) {
                console.log(validLocationFilters)
                businessQuery.$or = validLocationFilters;
            }
        }
        if (keyword && typeof keyword === 'string') {
            const matchingIndustries = await industries.find({
                $or: [
                    { industry: { $regex: keyword, $options: 'i' } },
                    {  relatedFields: { $regex: new RegExp(keyword as string, 'i') }}
                ]
            });
            const Industries = matchingIndustries.map((ind) => ind.industry);
            if (Industries.length > 0) {
                businessQuery.industry = { $in: Industries };
            }
        }
        console.log(businessQuery)
        const totalBusinesses = await businessLeads.countDocuments(businessQuery);
        const businesses = await businessLeads
          .find(businessQuery)
          .select('name industry description town postcode business_functions requiredCredit')
          .skip((Number(page) - 1) * Number(limit))
          .limit(Number(limit));
        
        if (!businesses.length) {
            return res.status(404).json({
                success: false,
                message: 'No businesses found matching the industry and location provided.'
            });
        }
        return res.status(200).json({
            success: true,
            data: businesses.map(business => {
                return {
                    ...business,
                    name: HiddenTextRenderer.toNameHashed(business.name),
                    // block out the name of the business from the description
                    description: HiddenTextRenderer.toDescriptionHashed(business.description, business.name.trim().split(' ')),
                    postcode: HiddenTextRenderer.toPostCodeHashed(business.postcode)
                }
            }),
            page: Number(page),
            limit: Number(limit),
            totalPages: Math.ceil(totalBusinesses / Number(limit)),
            totalBusinesses
        });
    } catch (error) {
        console.log(error)
        next(error);
        return res.status(500).json({ success: false, error: 'Internal Server Error' });
    }
};

export const getLeadById = async (req: Request, res: Response, next: NextFunction): Promise<Response | void> => {
    const { Id } = req.params;

    try {
        const Lead = await businessLeads.findById(Id);
        if (!Lead) {
            return res.status(404).json({
                success: false,
                message: 'Business not found'
            });
        }
        return res.status(200).json({
            success: true,
            Lead: {
                ...Lead,
                name: HiddenTextRenderer.toNameHashed(Lead.name),
                // block out the name of the business from the description
                description: HiddenTextRenderer.toDescriptionHashed(Lead.description, Lead.name.trim().split(' ')),
                postcode: HiddenTextRenderer.toPostCodeHashed(Lead.postcode),
                email_addresses: Lead.email_addresses?.map((email) => HiddenTextRenderer.toEmailHashed(email)) || [],
                telephone_numbers: Lead.telephone_numbers.map((phone) => HiddenTextRenderer.toNumberHashed(phone))
            } as typeof Lead
        });
    } catch (error) {
        console.log(error)
        next(error);
        return res.status(500).json({ success: false, error: 'Internal Server Error' });
    }
};

export const upsertLeadsCart = async (req: any, res: Response, next: NextFunction): Promise<Response | void> => {
    try {
        const userId = req.user._id
        const { leadIds, cartId } = req.body;

        let cart;
        const Leads = await businessLeads.find({ _id: { $in: leadIds } });
        const totalAmountToAdd:number = Leads.reduce((sum, lead) => sum + lead.requiredCredit, 0);
        if (cartId) {
            cart = await leadCart.findOneAndUpdate(
                { _id: cartId, user: userId, status: 'pending' },
                { $addToSet: { leads: { $each: leadIds } },
                    $inc: { totalAmount: totalAmountToAdd },
                 },
                { new: true }
            )
            if (!cart) {
                return res.status(404).json({ error: 'Pending cart not found.' });
            }
        } else {
            cart = await leadCart.create({
                user: userId,
                leads: leadIds,
                status: 'pending',
                totalAmount:totalAmountToAdd
            });
        }
        return res.status(201).json(cart);
    } catch (error) {
        next(error)
        console.log(error)
        return res.status(500).json({ error: 'Error creating cart.' });
    }
}

export const getPendingCart = async (req: any, res: Response, next: NextFunction): Promise<Response | void> => {
    try {
        const userId = req.user._id
        const pendingCart = await leadCart.findOne({user: userId, status: 'pending' }).populate({
            path: 'leads',
            select: 'name industry description requiredCredit' 
        })
        return res.status(201).json(pendingCart);
    } catch (error) {
        next(error)
        console.log(error)
        return res.status(500).json({ error: 'Error getting cart.' });
    }
}

export const getLeadsCart = async (req: any, res: Response, next: NextFunction): Promise<Response | void> => {
    try {
        const userId = req.user._id
        const leadCarts = await leadCart.find({user:userId}).populate({
            path: 'leads',
            select: 'name industry description requiredCredit' 
        })
        return res.status(201).json(leadCarts);
    } catch (error) {
        next(error)
        console.log(error)
        return res.status(500).json({ error: 'Error getting cart.' });
    }
}
export const getPurhasedLeadsCart = async (req: any, res: Response, next: NextFunction): Promise<Response | void> => {
    try {
        const userId = req.user._id
        const leadCarts = await leadCart.find({user:userId,status: 'purchased'}).populate({
            path: 'leads',
            select: 'name industry address town requiredCredit postcode email_addresses telephone_numbers'
        })
        return res.status(201).json(leadCarts);
    } catch (error) {
        next(error)
        console.log(error)
        return res.status(500).json({ error: 'Error getting cart.' });
    }
}
export const getPurhasedLeads = async (req: any, res: Response, next: NextFunction): Promise<Response | void> => {
    try {
        const userId = req.user._id
        const purchaseLeads = await purchasedLead.find({user:userId})
        .populate({
            path: 'leads',
            select: 'name industry description requiredCredit' 
        })
        return res.status(201).json(purchaseLeads);
    } catch (error) {
        next(error)
        console.log(error)
        return res.status(500).json({ error: 'Error getting cart.' });
    }
}


export const checkoutLeadCart = async (req: any, res: Response, next: NextFunction): Promise<Response | void> => {
    try {
        const userId = req.user._id
        const {cartId} = req.params;
        const user = await User.findOne({_id:userId});
        if (!user) {
            return res.status(404).json({ error: 'User not found.' });
        }
        const cart = await leadCart.findOne({_id:cartId, user:userId});
        if (!cart || cart.status === 'purchased') {
            return res.status(400).json({ error: 'Cart not found or already purchased.' });
        }
        if (user.totalCredit < cart.totalAmount) {
            return res.status(400).json({ error: 'Insufficient credit to complete the purchase.' });
        }
        user.totalCredit -= cart.totalAmount;
        await user.save();
        const transaction  = await Transaction.create({
            userId: userId,
            transactionType: 'debit',
            amount: cart.totalAmount,
            reason: 'Purchase of business leads'
        });
        const purchase = await purchasedLead.findOneAndUpdate(
            { user: userId },
            {
                $addToSet: { leads: { $each: cart.leads } },                  
            },
            { new: true, upsert: true }
        );
        cart.status = 'purchased';
        cart.purchaseDate = new Date();
        await cart.save();
        return res.status(200).json({ message: 'Purchase successful.', 
            purchase,
            transactionId: transaction.transactionId
        });
    } catch (error) {
        next(error)
        console.log(error)
        return res.status(500).json({ error: 'Error during checkout.' });
    }
};

