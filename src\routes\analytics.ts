import { Router } from 'express';
import { authMiddleware } from '../middleware/authService';
import { trackPageView } from '../controllers/analytics/pageView';
import { trackPageSession } from '../controllers/analytics/pageSession';
import { trackConversion } from '../controllers/analytics/conversion';
import { trackElementInteraction } from '../controllers/analytics/elementInteraction';
import { GetAppointmentReturnRate, GetCurrentMonthAppointmentRevenue, GetTodayAppointmentRenvenue, getAverageRevenue, getClientCountAndPercentage, getClientsForReEngagement, getHighestAverageRevenueService, getHighestTotalRevenueService, getInactiveClients, getMostUsedServices, getReEngagementStrategies, getServiceReturnRateMonthly, getServiceReturnRateQuarterly } from '../controllers/analytics/clientAppointment';
const router: Router = Router();

router.post('/page-view/track',authMiddleware,trackPageView)
router.post('/page-session/track',authMiddleware,trackPageSession)
router.post('/conversion/track',authMiddleware,trackConversion)
router.post('/interaction/track',authMiddleware,trackElementInteraction)

router.get('/appointment-renvenue/today',authMiddleware,GetTodayAppointmentRenvenue)
router.get('/appointment-renvenue/current-month',authMiddleware,GetCurrentMonthAppointmentRevenue)
router.get('/appointment/return-rate/:serviceId',authMiddleware,GetAppointmentReturnRate)
router.get('/appointment/most-used-service',authMiddleware,getMostUsedServices)
router.get('/appointment/client-percentage/:serviceId',authMiddleware,getClientCountAndPercentage)
router.get('/appointment/average-revenue-service/:serviceId',authMiddleware,getAverageRevenue)
router.get('/appointment/highest-average-revenue-service/',authMiddleware,getHighestAverageRevenueService)
router.get('/appointment/highest-total-revenue-service/',authMiddleware,getHighestTotalRevenueService)
router.get('/monthly-return-rate/service/:serviceId',authMiddleware,getServiceReturnRateMonthly)
router.get('/quarterly-return-rate/service/:serviceId',authMiddleware,getServiceReturnRateQuarterly)
router.get('/clients-reengagement/service/:serviceId',authMiddleware,getClientsForReEngagement)
router.get('/inactive-client/service/',authMiddleware,getInactiveClients)
router.get('/analytics/client/:clientId/re-engagement',authMiddleware,getReEngagementStrategies)

export default router;