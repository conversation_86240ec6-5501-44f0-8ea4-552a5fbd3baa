import mongoose, { Document, Schema } from 'mongoose';

export interface IPuzzlePrice extends Document {
  stripePriceId: string;
  stripeProductId:string;
  currency: string;
  unitAmount: number;
  interval: 'day' | 'week' | 'month' | 'year';
  productId:mongoose.Types.ObjectId;
}

const PuzzlePriceSchema: Schema = new Schema(
  {  
    productId:{ type: mongoose.Types.ObjectId, required: true, ref: 'PuzzleProduct' },
    stripePriceId: { type: String, required: true, unique: true },
    stripeProductId: { type: String, required: true},
    currency: { type: String, default: 'usd', required: true },
    unitAmount: { type: Number, required: true },
    interval: {
      type: String,
      enum: ['day', 'week', 'month', 'year'],
      required: true,
    },
  },
  { timestamps: true }
);

export default mongoose.model<IPuzzlePrice>('PuzzlePrice', PuzzlePriceSchema);
