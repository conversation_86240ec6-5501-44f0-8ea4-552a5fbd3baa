import mongoose,{ Schema, model, Document } from 'mongoose';

export interface IBooking extends Document {
    userId: mongoose.Types.ObjectId;// The business to which the booking belongs
    serviceId: mongoose.Types.ObjectId;// The service that was booked
    selectedServiceOptions: mongoose.Types.ObjectId[]; // The options selected for the service
    bookingType: 'internal' | 'external'; // To differentiate between internal and external bookings
    date: Date; // Date of the appointment
    timeSlot: string; // Time slot of the appointment (e.g., 10:00 - 11:00)
    bookedBy?: string; 
    customerInfo?: {
        firstName: string;
        lastName: string;
        email: string;
        phone: string;
    }; // Only applicable for external bookings
    notes?: string; // Additional notes for the booking
    status: 'pending' | 'confirmed' | 'canceled'; // Status of the booking
    googleEventId?: string; // Google Calendar event ID
    outlookEventId?: string; // Outlook Calendar event ID
    appleEventId?: string; // Apple Calendar event ID
    createdAt: Date;
    updatedAt: Date;
}

// Schema definition
const bookingSchema = new Schema<IBooking>(
    {
        userId: {
            type: Schema.Types.ObjectId,
            ref: 'User',
            required:true
        },
        serviceId: {
            type: Schema.Types.ObjectId,
            ref: 'Services',
            required: true,
        },
        selectedServiceOptions: {
            type: [Schema.Types.ObjectId],
            default: []
        },
        bookingType: {
            type: String,
            enum: ['internal', 'external'],
            required: true,
        },
        date: {
            type: Date,
            required: true,
        },
        timeSlot: {
            type: String,
            required: function() {
                return this.bookingType === 'external';
            }
        },
        bookedBy: {
            type: String,
            enum: ['business', 'client'],
            required: true,
        },
        googleEventId: {
            type: String,
        },
        outlookEventId: {
            type: String,
        },
        appleEventId: {
            type: String,
        },
        customerInfo: 
        {
            firstName: {
                type: String,
                required:true
            },
            lastName: {
                type: String,
                required:true
            },
            email: {
                type: String,
                required:true

            },
            phone: {
                type: String,
                required:true
            },
        },
        notes: {
            type: String,
        },
        status: {
            type: String,
            enum: ['pending', 'confirmed', 'canceled'],
            default: 'pending',
        },
    },
    {
        timestamps: true, // Automatically adds createdAt and updatedAt fields
    }
);
export default model<IBooking>('Booking', bookingSchema);
