import { Router } from 'express';
import { authMiddleware } from '../middleware/authService';
import { createCategory, deleteCategory, getCategories, getCategoryById, updateCategory } from '../controllers/category';
const router: Router = Router();

router.post('/category', authMiddleware,createCategory);
router.put('/update-service/:id', authMiddleware,updateCategory);
router.delete('/delete-service/:id', authMiddleware,deleteCategory);
router.get('/category', authMiddleware,getCategories);
router.get('/service/:id', authMiddleware,getCategoryById);

export default router;