import { Router } from 'express';
import { authMiddleware } from '../middleware/authService';
import { DeleteBusinessClient, createBusinessClients, getBusinessClients, getBusinessClientsbyId, updateBusinessClient } from '../controllers/businessClients';
const router: Router = Router();

router.get('/business-client',authMiddleware,getBusinessClients)
router.get('/business-client/:Id',authMiddleware,getBusinessClientsbyId)
router.put('/business-client/:Id',authMiddleware,updateBusinessClient)
router.delete('/business-client/:Id',authMiddleware,DeleteBusinessClient)
router.post('/business-client',authMiddleware,createBusinessClients)

export default router;