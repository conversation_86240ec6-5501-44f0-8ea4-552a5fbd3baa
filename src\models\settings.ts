import mongoose, { Document, Schema } from 'mongoose';

export interface ISettings extends Document {
  userId: mongoose.Types.ObjectId; // Reference to the user/business that owns the service
  Bookingsettings: {
    buttonColor?:string;
    description?:string;
    textColor?: string;
    backgroundColor?: string;
    backgroundImageUrl?: string;
  };
}

const settingsSchema: Schema = new Schema({
  userId: { type: mongoose.Types.ObjectId, required: true, ref: 'User',unique:true},
  Bookingsettings: {
    buttonColor:{
        type: String,
        default: '#FFFFFF' 
    },
    textColor: {
        type: String,
        default: '#000000' 
    },
    backgroundColor: {
        type: String,
        default: '#FFFFFF' 
    },
    description:String,
    backgroundImageUrl: String
  }

}, {
  timestamps: true // Automatically add createdAt and updatedAt fields
});

export default mongoose.model<ISettings>('Settings', settingsSchema);
