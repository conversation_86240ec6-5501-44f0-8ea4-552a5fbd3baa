import mongoose, { Schema, Document } from "mongoose";

export interface IMaintenanceMode extends Document {
  isActive: boolean; 
  message: string;   
  updatedAt: Date;
}

const maintenanceModeSchema = new Schema<IMaintenanceMode>({
  isActive: { type: Boolean, required: true, default: false },
  message: { type: String, required: false, default: "Maintenance for updates. Please check back later." },
  updatedAt: { type: Date, default: Date.now },
});

export default mongoose.model<IMaintenanceMode>(
  "MaintenanceMode",
  maintenanceModeSchema
);
