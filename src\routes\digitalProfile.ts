import { Router } from 'express';
import { authMiddleware } from '../middleware/authService';
import { addWebsiteLink, getDigitalProfile, getDigitalProfileByUsername, removeWebsiteLink, updateDigitalProfile, updateWebsiteLinkSettings, uploadDigitalProfileImage } from '../controllers/digitalProfile';
import { profileImageUpload } from '../utils/cloudinary';
import { uploadProfileImage } from '../controllers/user';
const router: Router = Router();

router.post('/upload-digital-image', authMiddleware, profileImageUpload.single('image'), uploadProfileImage)
router.post('/digital-profile/upload-image', authMiddleware, profileImageUpload.single('image'), uploadDigitalProfileImage)
router.get('/digital-profile',authMiddleware,getDigitalProfile)
router.post('/digital-profile',authMiddleware,addWebsiteLink)
router.put('/digital-profile',authMiddleware,updateDigitalProfile)

router.get('/digital-profile-username/:username',getDigitalProfileByUsername)
router.put('/digital-profile/website/:title',authMiddleware,updateWebsiteLinkSettings)
router.delete('/business-link/website/:title', authMiddleware, removeWebsiteLink);

export default router;