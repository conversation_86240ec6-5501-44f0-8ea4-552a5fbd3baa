import {Response, NextFunction } from 'express';
import booking from '../models/booking';
import services from '../models/services';
import externalAccount from '../models/externalAccount';
import { refreshAccessToken } from '../services/oauthService';
import { GoogleEventsDetails, addEventToGoogleCalendar } from '../services/calendarService';
import calendarSettings from '../models/calendarSettings';
import { addDurationToTime, combineDateAndTime } from '../utils/bookingUtils';
import { createClientForUser } from './businessClients';
import { createStripePaymentIntentForService, retrievePaymentIntentofInvoice } from '../services/stripesService';
import { calculateFeeInCents, convertToMinorUnits } from '../utils/stripes';
import User from '../models/user';
import paymentIntent from '../models/stripes/paymentIntent';
import clientAppointment from '../models/clientAppointment';
import { sendEmail } from '../services/emailService';

export const syncUpsertBookings = async (userId:string): Promise<void> =>{
    try {
        const GoogleAccount = await externalAccount.findOne({userId, name: "google",connect: true, activate: true});
        const AppleAccount = await externalAccount.findOne({userId, name: "apple",connect: true, activate: true});

        const now = new Date();
        const bookings = await booking.find({
          userId,
          status:'pending',
          date: { $gte: now }, 
        })
        for (const booking of bookings) {
            const service = await services.findById(booking.serviceId);
            const userCalendarSetting = await calendarSettings.findOne({userId})
            const googleEvent:GoogleEventsDetails = {
                title: `${service?.name} Booking`,
                location: "Puzzle Piece",
                description: `Booked By ${booking.customerInfo?.firstName || ''} ${booking.customerInfo?.lastName || ''}` ,
                startDateTime:combineDateAndTime(booking.date.toISOString(), booking.timeSlot),
                endDateTime: combineDateAndTime(booking.date.toISOString(), addDurationToTime(booking.timeSlot, service!.duration)),
                timeZone:userCalendarSetting?.timezone || 'Europe/London'
            }
            if (GoogleAccount) {
                const googleAccessToken = await refreshAccessToken(GoogleAccount, "google")
                if (googleAccessToken) {
                  const {eventId} = await addEventToGoogleCalendar(googleAccessToken, googleEvent, booking.googleEventId);
                  booking.googleEventId=eventId as string
                }
            }
            await booking.save()
        }
        

    } catch (error) {
        console.error('Error syncing bookings:', error);
    }
}
export const createExternalBooking = async (req: any, res: Response, next: NextFunction): Promise<Response | void> => {
    const { serviceId,serviceOptions, date, timeSlot, customerInfo, notes } = req.body;
    try {
        
        const service = await services.findById(serviceId).populate('userId', 'name email business_name');
        if(!service){
            return res.status(400).json({
                success:false,
                error:"Service is not available for booking"
            })
        }
        const userCalendarSetting = await calendarSettings.findOne({userId:service.userId._id})
        if(!userCalendarSetting){
            return res.status(400).json({
                success:false,
                error:"Not found"
            })
        }
        const newBooking = new booking({
            bookingType: 'external',
            userId: service.userId._id,
            serviceId,
            date,
            timeSlot,
            customerInfo,
            notes,
            bookedBy:"client",
            selectedServiceOptions: serviceOptions
        });

        const googleEvent:GoogleEventsDetails = {
            title: `${service.name} Booking`,
            location: "Puzzle Piece",
            description: `Booked By ${customerInfo.name}` ,
            startDateTime:combineDateAndTime(date, timeSlot),
            endDateTime: combineDateAndTime(date, addDurationToTime(timeSlot, service.duration)),
            timeZone:userCalendarSetting.timezone
        }
        

        const clientData={
            clientName: `${newBooking.customerInfo?.firstName || ''} ${newBooking.customerInfo?.lastName || ''}`,
            clientEmail:newBooking.customerInfo?.email!,
            phoneNumber:newBooking.customerInfo?.phone!,
            lastAppointmentDate:date
        }
        // Calculate base service price
        let appointmentRevenue = service.price;

        if (newBooking.selectedServiceOptions && newBooking.selectedServiceOptions.length > 0 && service.options) {
            newBooking.selectedServiceOptions.forEach(optionId => {
            const selectedOption = service.options?.find(option => 
              (option._id as any).toString() === optionId.toString());
            if (selectedOption) {
                appointmentRevenue += selectedOption.price;
            }
          });        
        }
        const client = await createClientForUser(service.userId._id.toString(),clientData)
        const newClientAppointment = new clientAppointment({
            userId:service.userId,
            clientId:client?._id,
            service:service._id,
            date:date,
            revenue:appointmentRevenue
        })
        await newClientAppointment.save()
        const GoogleAccount = await externalAccount.findOne({userId:service.userId._id, name: "google",connect: true, activate: true});
        if (GoogleAccount) {
            const googleAccessToken = await refreshAccessToken(GoogleAccount, "google")
            if (googleAccessToken) {
              const {eventId}=await addEventToGoogleCalendar(googleAccessToken, googleEvent);
              newBooking.googleEventId=eventId as string
            }
        }
        await sendEmail(
            newBooking.customerInfo?.email!,
            "Booking Confirmation",
            "serviceBooking.hbs",
            {
                userName: `${newBooking.customerInfo?.firstName || ''} ${newBooking.customerInfo?.lastName || ''}`,
                businessName:(service.userId as any).business_name as string,
                serviceName:service.name,
                date:date,
                time:timeSlot
            }
        )
        //Email to business owner
        
        await sendEmail(
            (service.userId as any).email,
            "New Booking Received",
            "newBooking.hbs",
            {
                userName: `${newBooking.customerInfo?.firstName || ''} ${newBooking.customerInfo?.lastName || ''}`,
                businessName:(service.userId as any).business_name as string,
                serviceName:service.name,
                date:date,
                time:timeSlot,
                notes:newBooking.notes,
                dashboardLink:`${process.env.FRONTENDURL}/scheduling/dashboard`
            }
        )
        const savedBooking = await newBooking.save();
        return res.status(201).json({
            success: true,
            message: 'External booking created successfully',
            booking: savedBooking,
        });
    } catch (error:any) {
        next(error);
        console.error('Error creating external booking:', error);
        return res.status(500).json({ error: 'Error creating external booking', message:error?.message});
    }
};

export const createInternalBooking = async (req: any, res: Response, next: NextFunction): Promise<Response | void> => {
    const {serviceId,serviceOptions, date, timeSlot, notes, customerInfo } = req.body;
    const userId = req.user?._id; // Assuming user info is stored in req.user after authentication

    try {
        const userCalendarSetting = await calendarSettings.findOne({userId})
        if(!userCalendarSetting){
            return res.status(400).json({
                success:false,
                error:"Not found"
            })
        }
        const service = await services.findById(serviceId)
        if(!service){
            return res.status(400).json({
                success:false,
                error:"Service is not available for booking"
            })
        }

        // Create a new internal booking
        const newBooking = new booking({
            bookingType: 'internal',
            userId,
            serviceId,
            date,
            timeSlot:`${timeSlot}-${addDurationToTime(timeSlot, service.duration)}`,
            bookedBy:"business",
            notes,
            customerInfo,
            selectedServiceOptions: serviceOptions
        });
        const googleEvent:GoogleEventsDetails = {
            title: `${service.name} Booking`,
            location: "Puzzle Piece",
            description: `Booked By ${customerInfo.name}` ,
            startDateTime:combineDateAndTime(date, timeSlot),
            endDateTime: combineDateAndTime(date, addDurationToTime(timeSlot, service.duration)),
            timeZone:userCalendarSetting.timezone
          }
        const clientData={
            clientName: `${newBooking.customerInfo?.firstName || ''} ${newBooking.customerInfo?.lastName || ''}`,
            clientEmail:newBooking.customerInfo?.email!,
            phoneNumber:newBooking.customerInfo?.phone!,
            lastAppointmentDate:date
        }
        let appointmentRevenue = service.price;

        if (newBooking.selectedServiceOptions && newBooking.selectedServiceOptions.length > 0 && service.options) {
            newBooking.selectedServiceOptions.forEach(optionId => {
            const selectedOption = service.options?.find(option => 
              (option._id as any).toString() === optionId.toString());
            if (selectedOption) {
                appointmentRevenue += selectedOption.price;
            }
          });        
        }
        const client  = await createClientForUser(service.userId.toString(),clientData)
        const newClientAppointment = new clientAppointment({
            userId:service.userId,
            clientId:client?._id,
            service:service._id,
            date:date,
            revenue:appointmentRevenue
        })
        await newClientAppointment.save()
        const GoogleAccount = await externalAccount.findOne({ userId, name: "google",connect: true, activate: true});
        if (GoogleAccount) {
            const googleAccessToken = await refreshAccessToken(GoogleAccount, "google")
            if (googleAccessToken) {
              const {eventId} = await addEventToGoogleCalendar(googleAccessToken, googleEvent);
              newBooking.googleEventId=eventId as string
            }
        }
        const savedBooking = await newBooking.save();
        return res.status(201).json({
            success: true,
            message: 'Internal booking created successfully',
            booking: savedBooking,
        });
    } catch (error:any) {
        next(error);
        console.error('Error creating internal booking:', error);
        return res.status(500).json({ error: 'Error creating internal booking', message:error?.message});
    }
};


export const getBookings = async (req: any, res: Response, next: NextFunction): Promise<Response> => {
    try {
        const servicesCreatedByUser = await services.find({ userId: req.user._id }).select('_id');
        const serviceIds = servicesCreatedByUser.map(service => service._id);
        const bookings = await booking.find({ serviceId: { $in: serviceIds } }).populate('serviceId', 'name color')
        return res.status(200).json({ success: true, bookings });
    } catch (error) {
        next(error);
        return res.status(500).json({ success: false, error: 'Internal Server Error' });
    }
};

export const payBusinessService = async (req: any, res: Response, next: NextFunction): Promise<Response | void> => {
    const {clientEmail,serviceId,serviceOptions}= req.body
    try {
        const service = await services.findById(serviceId)
        if (!service) {
            return res.status(400).json({
              success: false,
              error: 'Service not found'
            })
        }
        const user = await User.findById(service.userId)
        if (!user) {
          return res.status(400).json({
            success: false,
            error: 'User not found'
          });
        }
         // Calculate total price including service options
         let totalPrice = service.price;
        
         // Add prices of selected service options if they exist
         if (serviceOptions && serviceOptions.length > 0 && service.options) {
             for (const optionId of serviceOptions) {
                 const selectedOption = service.options.find((option: { _id: any}) => 
                     option._id.toString() === optionId.toString() );
                 if (selectedOption) {
                     totalPrice += selectedOption.price;
                 }
             }
         }
        const serviceOwnerAccountId:string = user.stripesAccountId
        const serviceOwnerId: string = (user._id as string).toString(); 
        const metadata = {
            serviceOwnerId,
            clientEmail,
            serviceOptions: JSON.stringify(serviceOptions)
        };
        const applicationFee = calculateFeeInCents(convertToMinorUnits(totalPrice))
        console.log({
            price:convertToMinorUnits(totalPrice),
            currency :'eur',
            metadata,
            clientEmail,
            applicationFee,
            serviceOwnerAccountId
        })
        const newPaymentIntent = await createStripePaymentIntentForService(
            convertToMinorUnits(totalPrice),
            'usd',
            metadata,
            clientEmail,
            applicationFee,
            serviceOwnerAccountId
        )
        console.log(newPaymentIntent)
        const newpaymentintentData = new paymentIntent ({
            stripePaymentIntentId:newPaymentIntent.id,
            intentType:'service',
            intentTypeId:service._id,
            email:clientEmail,
            clientSecret:newPaymentIntent.client_secret,
            status:newPaymentIntent.status
        })
        await newpaymentintentData.save()
        if (newPaymentIntent) {
            return res.status(200).json({
                success: true,
                message: "Payment intent created",
                clientSecret: newPaymentIntent.client_secret
            });
        } else {
            return res.status(500).json({
                success: false,
                error: "Failed to create payment intent"
            });
        }

    
    } catch (error:any) {
     next(error);
     console.error('Error setting up service payment:', error);
     return res.status(500).json({ error: 'Error setting up service payment', message:error?.message});
    } 
}

export const payBusinessServiceOnSuccess = async (req: any, res: Response, next: NextFunction): Promise<Response | void> => {
    try {
        // const userId = req.user?._id
        const {paymentIntentId} = req.body
        const existStripePaymentIntent = await retrievePaymentIntentofInvoice(paymentIntentId)
        // const saleOwnerId = existStripePaymentIntent.metadata.saleOwnerId
        const clientEmail = existStripePaymentIntent.metadata.clientEmail
        // console.log(saleOwnerId,clientEmail)
        const paymentIntentData = await paymentIntent.findOne({
          stripePaymentIntentId:paymentIntentId,
          email:clientEmail
          // intentType:'service'
        })
        if (paymentIntentData){
          paymentIntentData.status = existStripePaymentIntent.status
          await paymentIntentData.save()
        }
        return res.status(200).json({
            success:true,
            message:'Completed'
        })
      } catch (error) {
        next(error)
        console.log("service payment error", error)
        return res.status(500).json({ error: 'Error using credits' });
      }
}

export const UpdateBookingStatus = async (req: any, res: Response, next: NextFunction): Promise<Response> => {
    try {
        const userId = req.user?._id
        const { bookingId } = req.params;
        const {status } = req.body;
        const bookingData = await booking.findOneAndUpdate({
            _id: bookingId,
            userId: userId,
            }, { status }, { new: true }).populate('serviceId', 'name');
        if (!bookingData) {
            return res.status(404).json({ error: 'Booking not found' });
        }
        if (status === 'canceled' && bookingData.customerInfo?.email) {
            await sendEmail(
                bookingData.customerInfo.email,
                'Booking Status Update',
                'cancelBooking.hbs',
                {
                    userName: `${bookingData.customerInfo?.firstName || ''} ${bookingData.customerInfo?.lastName || ''}`,
                    serviceName: (bookingData.serviceId as any).name,
                    date: bookingData.date,
                    time: bookingData.timeSlot,
                    notes: bookingData.notes,
                }
            );
        }
        return res.status(200).json({ success: true, booking: bookingData });
    } catch (error) {
        next(error);
        console.error('Error updating booking status:', error);
        return res.status(500).json({ error: 'Internal Server Error' });
    }
};

export const rescheduleBooking = async (req: any, res: Response, next: NextFunction): Promise<Response> => {
    try {
        const userId = req.user?._id;
        const { bookingId } = req.params;

        // Fetch User and Calendar Settings in parallel
        const [user, CalendarSettings] = await Promise.all([
            User.findById(userId),
            calendarSettings.findOne({ userId })
        ]);

        if (!user) {
            return res.status(404).json({ error: 'User not found' });
        }

        if (!CalendarSettings) {
            return res.status(404).json({ error: 'Calendar settings not found' });
        }

        const { newDate, timeSlot } = req.body;

        const bookingData = await booking.findOneAndUpdate(
            { _id: bookingId, userId },
            { date: newDate, timeSlot },
            { new: true }
        ).populate('serviceId', 'name');

        if (!bookingData) {
            return res.status(404).json({ error: 'Booking not found' });
        }

        if (bookingData.customerInfo?.email) {
            await sendEmail(
                bookingData.customerInfo?.email,
                'Booking Rescheduled',
                'rescheduleBooking.hbs',
                {
                    userName: `${bookingData.customerInfo?.firstName || ''} ${bookingData.customerInfo?.lastName || ''}`,
                    serviceName: (bookingData.serviceId as any).name,
                    businessName: user.business_name,
                    businessTimeZone: CalendarSettings.timezone,
                    newDate,
                    newTime: timeSlot
                }
            );
        }
        return res.status(200).json({ success: true, booking: bookingData });
    } catch (error) {
        next(error);
        console.error("reschdule error",error);
        return res.status(500).json({ error: 'Internal Server Error' });
    }
};
