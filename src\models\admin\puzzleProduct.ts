import mongoose, { Document, Schema } from 'mongoose';

export interface IPuzzleProduct extends Document {
  stripeProductId:string;
  name: string;
  description?: string;
  active: boolean;
  features:string[];
}

const PuzzleProductSchema: Schema = new Schema(
  {
    stripeProductId: { type: String, required: true, unique: true },
    name: { type: String, required: true },
    description: { type: String },
    active: { type: Boolean, default: true },
    features:[String]
  },
  { timestamps: true }
);

export default mongoose.model<IPuzzleProduct>('PuzzleProduct', PuzzleProductSchema);
