import { Router } from 'express';
import { createExternalBooking, createInternalBooking, getBookings, payBusinessService, payBusinessServiceOnSuccess, rescheduleBooking, UpdateBookingStatus } from '../controllers/booking';
import { authMiddleware } from '../middleware/authService';
const router: Router = Router();

router.post('/create-bookings/internal',authMiddleware,createInternalBooking)
router.put('/bookings/:bookingId/status',authMiddleware,UpdateBookingStatus)
router.put('/bookings/:bookingId/reschdule',authMiddleware,rescheduleBooking)


router.post('/create-bookings/external',createExternalBooking)
router.get('/bookings',authMiddleware,getBookings)
router.post('/pay-service',payBusinessService)
router.post('/pay-service/success',payBusinessServiceOnSuccess)

export default router;