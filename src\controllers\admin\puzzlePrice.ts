import puzzlePrice from "../../models/admin/puzzlePrice";
import PuzzleProduct from "../../models/admin/puzzleProduct";
import { createStripePrice } from "../../services/stripesService";
import { Response, Request } from "express";

export const createPuzzlePrice = async (req: Request, res: Response):Promise<Response> => {
    try {
        const { productId, currency, amount, interval } = req.body;

        const product = await PuzzleProduct.findById(productId);
        if (!product || !product.stripeProductId) {
          return res.status(404).json({ error: 'Product not found or missing Stripe product ID' });
        }
        const stripePrice = await createStripePrice(product.stripeProductId, currency,amount,interval)
    
        const newPrice = new puzzlePrice({
          stripePriceId: stripePrice.id,
          stripeProductId: product.stripeProductId,
          currency,
          unitAmount:amount,
          interval,
          productId: product._id,
        });
    
        const savedPrice = await newPrice.save();
    
        return res.status(201).json({
          message: 'Price created successfully!',
          price: savedPrice,
        });
      } catch (err) {
        console.error('Error creating price:', err);
        return res.status(500).json({ error: 'Failed to create price', err});
      }
};

export const getAllPuzzlePrice = async (req: Request, res: Response):Promise<Response> => {
  try {
    const productPrices = await puzzlePrice.find()
    return res.status(200).json({
      message: 'Prices fetched successfully!',
      productPrices
    });
  } catch (err) {
    console.error('Error getting all prices:', err);
    return res.status(500).json({ error: 'Failed to fetch prices', err });
  }
}

export const getPuzzlePriceById = async (req: Request, res: Response):Promise<Response> => {
  try {
    const {Id} = req.params
    const productPrice = await puzzlePrice.findById(Id)
    return res.status(200).json({
      message: 'Price fetched successfully!',
      productPrice,
    });
  } catch (err) {
    console.error('Error getting price:', err);
    return res.status(500).json({ error: 'Failed to fetch price', err });
  }
}

export const updatePuzzlePrice = async (req: Request, res: Response):Promise<Response> => {
  try {
    const {currency, amount, interval } = req.body;
    const {Id} = req.params
    const product = await puzzlePrice.findByIdAndUpdate(Id, 
      {
        currency, amount, interval
      }, {new:true})
    return res.status(201).json({
      message: 'Price updated successfully!',
      product,
    });
  } catch (err) {
    console.error('Error updating price:', err);
    return res.status(500).json({ error: 'Failed to update price', err });
  }
}

export const deletePuzzlePrice = async (req: Request, res: Response):Promise<Response> => {
  try {
    const {Id} = req.params
    await puzzlePrice.findByIdAndDelete(Id)
    return res.status(200).json({
      message: 'Price deleted successfully!'
    });
  } catch (err) {
    console.error('Error deleting price:', err);
    return res.status(500).json({ error: 'Failed to delete price', err });
  }
}

