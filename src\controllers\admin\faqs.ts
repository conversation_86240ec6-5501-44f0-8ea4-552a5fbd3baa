import { NextFunction, Request, Response } from "express";
import faqsModel from "../../models/admin/faqs";

export const getAllFaqs = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<Response> => {
  try {
    const faqs = await faqsModel.find();
    return res.status(200).json({
      faqs,
    });
  } catch (error) {
    next(error);
    console.log("Error in fetching faqs", error);
    return res.status(500).json({ error: "Error fetching FAQs" });
  }
};

export const getFaqById = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<Response> => {
  try {
    const { Id } = req.params;
    const faq = await faqsModel.findById(Id);

    if (!faq) {
      return res.status(404).json({ error: "FAQ not found" });
    }

    return res.status(200).json({
      faq,
    });
  } catch (error) {
    console.log("Error fetching faqs", error);
    next(error);
    return res.status(500).json({ error: "Error fetching FAQ" });
  }
};

export const createFaq = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<Response> => {
  try {
    const { question, answer, category } = req.body;
    const newFaq = new faqsModel({ question, answer, category });
    await newFaq.save();
    return res.status(201).json({
      newFaq,
    });
  } catch (error) {
    console.log("error creating faq", error);
    next(error);
    return res.status(500).json({ error: "Error creating FAQ" });
  }
};

export const updateFaq = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<Response> => {
  try {
    const { Id } = req.params;
    const { question, answer, category } = req.body;
    const updatedFaq = await faqsModel.findByIdAndUpdate(
      Id,
      { question, answer, category },
      { new: true }
    );
    if (!updatedFaq) return res.status(404).json({ error: "FAQ not found" });
    return res.status(200).json({
      updateFaq,
    });
  } catch (error) {
    next(error);
    console.log("Error updating faq", error);
    return res.status(500).json({ error: "Error updating FAQ" });
  }
};

export const deleteFaq = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<Response> => {
  try {
    const { Id } = req.params;

    const deletedFaq = await faqsModel.findByIdAndDelete(Id);

    if (!deletedFaq) {
      return res.status(404).json({ error: "FAQ not found" });
    }

    return res.status(200).json({ message: "FAQ deleted successfully" });
  } catch (error) {
    next(error);
    console.log("Error deleting faq", error);
    return res.status(500).json({ error: "Error deleting FAQ" });
  }
};
