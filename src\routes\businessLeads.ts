import { Router } from 'express';
import { authMiddleware } from '../middleware/authService';
import {checkoutLeadCart, getLeadById, getLeadsCart, getPendingCart, getPurhasedLeads, getPurhasedLeadsCart, searchBusinessesLeads, upsertLeadsCart } from '../controllers/businessLeads';
const router: Router = Router();

router.get('/leads',authMiddleware,searchBusinessesLeads)
router.get('/leads/:Id',authMiddleware,getLeadById)

router.post('/leads/buy',authMiddleware,upsertLeadsCart)
router.get('/leads/carts',authMiddleware,getLeadsCart)
router.get('/leads/carts/purchased',authMiddleware,getPurhasedLeadsCart)
router.get('/leads/cart/pending',authMiddleware,getPendingCart)
router.get('/leads/checkout/:cartId',authMiddleware,checkoutLeadCart)


router.get('/leads-purchased',authMiddleware,getPurhasedLeads)

export default router;