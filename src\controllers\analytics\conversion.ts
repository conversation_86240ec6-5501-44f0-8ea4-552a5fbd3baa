import {  Response, NextFunction } from 'express';
import conversion from '../../models/analytics/conversion';

export const trackConversion = async (req: any, res: Response, next: NextFunction): Promise<Response> => {
    const {conversionType } = req.body;
    const userId = req.user._id
    try {
        const newConversion =  new conversion({
            userId,
            conversionType
        })
        await newConversion.save()
        return res.status(201).json({
            success:true,
            message:"Conversion Added"
        })
    } catch (error) {
        console.error('Error tracking conversion:', error);
        next(error); 
        return res.status(500).json({ success: false, error: 'Server error' });
    }
}