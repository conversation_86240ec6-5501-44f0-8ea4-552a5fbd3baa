import { Router } from "express";
import { authMiddleware } from "../middleware/authService";
import {
  FetchPaymentMethod,
  PurchaseCreditOnSuccess,
  purchaseCredits,
} from "../controllers/wallet";
import { getAllCreditPackages } from "../controllers/admin/puzzleCredit";
const router: Router = Router();

router.post("/purchase-credit", authMiddleware, purchaseCredits);
router.get("/credit-packages", authMiddleware, getAllCreditPackages);
router.get("/payment-method", authMiddleware, FetchPaymentMethod);
router.post(
  "/purchase-credit/success",
  authMiddleware,
  PurchaseCreditOnSuccess
);

export default router;
