import mongoose, { Schema, Document } from 'mongoose';

export interface IUserFeedback extends Document {
  fullName:string;
  email:string;
  feedback:string;
  type: 'problem' | 'suggestion' | 'other';
}

const userFeedbackSchema: Schema = new Schema({
    fullName: {
        type: String,
        required: true,
        trim: true,
    },
    type: {
      type: String,
      required: true,
      default:'other',
      enum: ['problem', 'suggestion','other']
    },
    email: {
        type: String,
        required: true,
        trim: true,
        validate: {
          validator: (email) => {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return emailRegex.test(email);
          },
          message: 'Invalid email address.',
        },
    },
    feedback: {
        type: String,
        required: true,
        trim: true,
        minlength: 10,
      },
}, { timestamps: true });

export default mongoose.model<IUserFeedback>('UserFeedbacks', userFeedbackSchema);
