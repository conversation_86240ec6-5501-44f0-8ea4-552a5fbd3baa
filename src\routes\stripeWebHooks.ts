import express, { Request, Response } from 'express';
import bodyParser from 'body-parser';
import { stripeClient } from '../services/stripesService';
import { addUserCredit } from '../utils/webhooksFunctions';
const router = express.Router();

if(!process.env.STRIPE_WEBHOOK_SECRET_KEY){
  throw new Error ('stripe webhook is missing in local environment')
}

// Use body-parser to parse the incoming webhook request
// router.post('/webhooks', bodyParser.json(), async (req:Request, res:Response) => {
//   let event;
//   try {
//     // Verify the webhook signature
//     event = stripeClient.webhooks.constructEvent(req.body, req.headers['stripe-signature'], process.env.STRIPE_WEBHOOK_SECRET);
//   } catch (err:any) {
//     console.error(`Webhook signature verification failed: ${err.message}`);
//     return res.status(400).send(`Webhook Error: ${err.message}`);
//   }

//   switch (event.type) {
//     case 'payment_intent.succeeded':
//       const paymentIntent = event.data.object;
//       const userId = paymentIntent.metadata.userId;
//       const creditId = paymentIntent.metadata.creditId;
//       //TODO: REMOVE THE FUNCTION IN WALLET CONTROLLER PAYMENT ON SUCCESS WHEN THE WEBHOOK IS ACTIVATED
//       if(userId && creditId){
//         await addUserCredit(userId, creditId)
//       }
//       break;
//     case 'payment_method.attached':
//       const paymentMethod = event.data.object;
//       break;
//     case 'customer.subscription.created':
//       const subscriptionCreated = event.data.object;
//       break;  
//     case 'customer.subscription.deleted':
//       const subscriptionDeleted = event.data.object;
//       break;
//     case 'customer.subscription.updated':
//       const subscriptionUpdated = event.data.object;
//       break;
//     case 'invoice.upcoming':
//       break;
//     case 'invoice.payment_succeeded':
//       const invoiceSuccess = event.data.object;
//       break;
//     case 'invoice.payment_failed':
//       const failedInvoice = event.data.object;
//       break;
//     // Add more cases as needed
//     default:
//       console.warn(`Unhandled event type: ${event.type}`);
//       res.status(400).send({ error: 'Invalid event' });
//   }

//   res.status(200).json({ received: true });
// });

export default router;
