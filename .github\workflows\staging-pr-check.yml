name: Backend Staging PR Check

on:
  pull_request:
    branches:
      - dev

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout repository
        uses: actions/checkout@v3

      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '20'
          cache: 'npm'
          cache-dependency-path: 'package-lock.json'

      - name: Install dependencies
        run: npm ci

      - name: Build application
        run: npm run build

      - name: Build Docker image
        run: |
          docker build -t puzzle-piece-backend:pr-check .
          docker images puzzle-piece-backend:pr-check

      - name: Test Docker image
        run: |
          docker run --name test-container -d puzzle-piece-backend:pr-check
          sleep 5
          docker ps | grep test-container
          docker logs test-container
          docker stop test-container
          docker rm test-container