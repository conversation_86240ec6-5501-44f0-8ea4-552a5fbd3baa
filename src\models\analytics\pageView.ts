import mongoose, { Schema, Document } from 'mongoose';

interface IPageView extends Document {
    userId: mongoose.Types.ObjectId;
    pageUrl:string;
}

const pageViewSchema: Schema = new Schema({
    userId: { type: Schema.Types.ObjectId, ref: 'User', required: true },
    pageUrl: { type: String, required: true },
},{ timestamps: true });
export default mongoose.model<IPageView>('PageView', pageViewSchema);
