import { Response,NextFunction } from "express";
import category from "../models/category";
import { IService } from "../models/services";
import services from "../models/services";
import { generateUrlToken } from "../utils/urlTokenGenrator";
import User from "../models/user";

export const createService = async (req: any, res: Response, next: NextFunction): Promise<Response> => {
    const urlToken = generateUrlToken(); // Generate a unique token for the service
    const bookingUrl = `${process.env.FRONTENDURL}/bookings/direct/${urlToken}`; // Generate the direct booking link
    let userId;

    if (req.user) {
        userId = req.user?._id; // Get userId from the authenticated user
    }

    const {
        name,
        description,
        messageAfterScheduling,
        duration,
        blockExtraTimeBefore,
        blockExtraTimeAfter,
        price,
        category: categoryId,
        color,
        picture,
        access,
        isGroupEvent,
        serviceOptions,
    } = req.body;

    try {
        // Check if the category exists for the user
        const checkCategory = await category.findOne({ _id: categoryId, userId: userId });
        if (!checkCategory) {
            return res.status(400).json({
                success: false,
                error: 'Invalid Category'
            });
        }

        // Create the new service with the generated urlToken and booking link
        const newService = new services({
            userId,
            name,
            description,
            messageAfterScheduling,
            duration,
            blockExtraTimeBefore,
            blockExtraTimeAfter,
            price,
            category: checkCategory._id,
            color,
            picture,
            access,
            isGroupEvent,
            urlToken,  // Add the urlToken to the service
            directBookingLink: bookingUrl, // Add the direct booking link to the service
            options:serviceOptions
        });

        const savedService = await newService.save();

        return res.status(201).json({
            success: true,
            message: 'Service created successfully',
            service: savedService,
            directBookingLink: bookingUrl 
        });
    } catch (error) {
        next(error);
        return res.status(500).json({
            success: false,
            error: 'Internal Server Error'
        });
    }
};

export const updatedService = async (req: any, res: Response, next: NextFunction): Promise<Response> => {
    const userId = req.user?._id; 
    const { id } = req.params; 
    const { ...updateFields }:IService= req.body as unknown as IService;
    try {
        // Find the service by ID and userId in one query
        const serviceToUpdate = await services.findOne({ _id: id, userId });
        if (!serviceToUpdate) {
            return res.status(404).json({
                success: false,
                error: 'Service not found'
            });
        }

        if (updateFields.category) {
            const validCategory = await category.findOne({ _id: updateFields.category, userId });
            if (!validCategory) {
                return res.status(400).json({
                    success: false,
                    error: 'Invalid Category'
                });
            }
        }

        Object.assign(serviceToUpdate, updateFields);
        // Save the updated service
        const updatedService = await serviceToUpdate.save();

        return res.status(200).json({
            success: true,
            message: 'Service updated successfully',
            service: updatedService
        });
    } catch (error) {
        next(error);
        return res.status(500).json({
            success: false,
            error: 'Internal Server Error'
        });
    }
};


export const deleteService = async (req: any, res: Response, next: NextFunction): Promise<Response> => {
    const userId = req.user?._id; 
    const { id } = req.params;

    try {
        const serviceToDelete = await services.findOne({ _id: id, userId });
        if (!serviceToDelete) {
            return res.status(404).json({
                success: false,
                error: 'Service not found'
            });
        }
        await serviceToDelete.deleteOne();
        return res.status(200).json({
            success: true,
            message: 'Service deleted successfully'
        });
    } catch (error) {
        next(error);
        return res.status(500).json({
            success: false,
            error: 'Internal Server Error'
        });
    }
};


export const getService = async (req: any, res: Response, next: NextFunction): Promise<Response> => {
    try {
        const Services = await services.find({ userId: req.user?._id }).populate(
            'category','name'
        );
        return res.status(200).json({ success: true, Services });
    } catch (error) {
        next(error);
        return res.status(500).json({ success: false, error: 'Internal Server Error' });
    }
};

export const getServiceById = async (req: any, res: Response, next: NextFunction): Promise<Response> => {
    const userId = req.user?._id
    const { id } = req.params;

    try {
        const Service = await services.findOne({_id:id, userId:userId});
        
        if (!Service) {
            return res.status(404).json({ success: false, error: 'Service not found' });
        }

        return res.status(200).json({ success: true, Service });
    } catch (error) {
        next(error);
        return res.status(500).json({ success: false, error: 'Internal Server Error' });
    }
};

export const getBookingServices = async (req: any, res: Response, next: NextFunction): Promise<Response> => {
    try {
        const { username } = req.params;
        const user = await User.findOne({ username });
        if (!user) {
          return res.status(404).json({ success: false, error: 'Invalid booking link ' });
        }
        const Services = await services.find({ userId: user?._id }).populate(
            'category','name'
        );
        return res.status(200).json({ success: true, Services, userId:user?._id });
        } catch (error) {
            next(error);
            return res.status(500).json({ success: false, error: 'Internal Server Error' });
        }
};


export const getDirectBookingService = async (req: any, res: Response, next: NextFunction): Promise<Response> => {
    try {
        const { urlToken } = req.params;
        console.log(urlToken)
        const Service = await services.findOne({ urlToken }).populate('userId', 'first_name last_name');
        if(!Service){
            return res.status(404).json({ success: false, error: 'Invalid booking link' });
        }
        const { userId } = Service;
        return res.status(200).json({ success: true, Service, userId});
    } catch (error) {
        console.log(error)
        next(error);
        return res.status(500).json({ success: false, error: 'Internal Server Error' });
    }
};

