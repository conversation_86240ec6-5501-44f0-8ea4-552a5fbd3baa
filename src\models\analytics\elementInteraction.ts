import mongoose, { Schema, Document } from 'mongoose';

interface IElementInteraction extends Document {
    userId: mongoose.Types.ObjectId;
    elementId:string;
    actionType:string;
}

const elementInteractionSchema: Schema = new Schema({
    userId: { type: Schema.Types.ObjectId, ref: 'User', required: true },
    elementId: { type: String, required: true },
    actionType: { type: String, required: true },
},{ timestamps: true });
export default mongoose.model<IElementInteraction>('ElementInteraction', elementInteractionSchema);
