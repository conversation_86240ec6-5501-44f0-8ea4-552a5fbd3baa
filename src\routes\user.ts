import { Router } from 'express';
import { CheckSubscriptionStatus, Login, Register, UpdateUserDetails, VerifyEmail, getUserDetailsWithCalendarSettings, requestPasswordReset, resetPassword, uploadProfileImage } from '../controllers/user';
import { authMiddleware } from '../middleware/authService';
import { profileImageUpload } from "../utils/cloudinary";
import { sendEmail } from "../services/emailService";

const router: Router = Router();

router.post('/upload-profile-image', authMiddleware, profileImageUpload.single('image'), uploadProfileImage)
router.post('/register', Register);
router.post('/login', Login);
router.get('/user/details',authMiddleware,getUserDetailsWithCalendarSettings);
router.get('/check-subscription-status',authMiddleware,CheckSubscriptionStatus);
router.put('/user/info',authMiddleware,UpdateUserDetails);
router.get('/auth/confirm-email/:token', VerifyEmail);
router.post('/auth/request-password-reset', requestPasswordReset);
router.post('/auth/reset-password/:token', resetPassword);

router.get('/auth/test-email', async (req, res) => {
  const token ='hyusdfehye8e3r';
  const planId = 'some-plan-id'; // Replace with actual plan ID
  const confirmationLink = `${process.env.FRONTENDURL}/confirm-email/${token}?planId=${planId}`;
  await sendEmail('<EMAIL>', "Confirm Your Email", "welcomeEmail.hbs", {
    name: '<EMAIL>',
    confirmationLink,
  });
    res.status(200).json({
        success: true,
        message: 'Email sent successfully',
    });
});
export default router;

