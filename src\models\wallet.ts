import mongoose, { Document, Schema } from 'mongoose';
import { v4 as uuidv4 } from 'uuid';


interface ITransaction extends Document {
  userId: mongoose.Types.ObjectId;
  transactionType: 'credit' | 'debit'; 
  reason:string;
  amount: number;
  createdAt: Date;
  transactionId:string;
}


// Transaction Schema
const TransactionSchema: Schema = new Schema(
  {
    userId: {
      type: mongoose.Types.ObjectId,
      required: true,
      ref: 'User',
    },
    reason:{
      type:String
    },
    transactionType: {
      type: String,
      enum: ['debit', 'credit'], 
      required: true,
    },
    amount: {
      type: Number,
      required: true,
    },
    createdAt: {
      type: Date,
      default: Date.now,
    },
  },
  {
    timestamps: true, 
  }
);

TransactionSchema.pre('save', function (next) {
  if (!this.transactionId) {
    this.transactionId = `PPC_${Date.now()}_${uuidv4()}`;
  }
  next();
});

const Transaction = mongoose.model<ITransaction>('Transaction', TransactionSchema);

export { Transaction,ITransaction };
