import mongoose, { Schema, Document } from 'mongoose';

interface IBusinessLeads extends Document {
  name: string;
  industry: string;
  field: string[];
  description: string;
  address: string;
  address2?: string;
  town: string;
  postcode: string;
  business_functions: string[];
  extraInformation?: Record<string, unknown>;
  email_addresses: string[];
  telephone_numbers: string[];
  url_maps: Array<{ website?: string; facebook?: string; x?: string }>;
  location: {
    type: 'Point';
    coordinates: [number, number];
    locationSet: boolean;
  };
  requiredCredit:number;
}

const BusinessLeads: Schema = new Schema({
  name: { type: String, required: true },
  industry: { type: String, required: true },
  field: { type: [String], required: true },
  description: { type: String, required: true },
  requiredCredit: { type: Number, required: true , default:10},
  address: { type: String, required: true },
  address2: { type: String, default: '' },
  town: { type: String, required: true },
  postcode: { type: String, required: true },
  business_functions: { type: [String], required: true },
  extraInformation: { type: Schema.Types.Mixed, default: {} },
  email_addresses: { type: [String], required: true },
  telephone_numbers: { type: [String], required: true },
  url_maps: [
    {
      website: { type: String },
      facebook: { type: String },
      x: { type: String },
    },
  ],
  location: {
    type: {
      type: String,
      enum: ['Point'],
      required: true,
    },
    coordinates: {
      type: [Number],
      required: true,
    },
    locationSet: { type: Boolean, required: true },
  },

});


// Create an index if needed
// BusinessSchema.index({ name: 1, postcode: 1, town: 1 });

// Export the model
export default mongoose.model<IBusinessLeads>('Business_Lead', BusinessLeads);


