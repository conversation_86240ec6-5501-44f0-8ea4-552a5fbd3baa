import { Response, Request, NextFunction } from "express";
import puzzleCredit from "../../models/admin/puzzleCredit";

export const createPuzzleCredit = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<Response> => {
  const { name, currency, creditAmount, amount } = req.body;
  try {
    const newCredit = new puzzleCredit({
      name,
      currency,
      creditAmount,
      amount,
    });
    await newCredit.save();
    return res.status(201).json({
      success: true,
      message: "Created Successfully",
      newCredit,
    });
  } catch (error) {
    next(error);
    console.error("Error creating price:", error);
    return res.status(500).json({ error: "Failed to create price" });
  }
};

export const getAllCreditPackages = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<Response> => {
  try {
    const credits = await puzzleCredit.find();
    return res.status(200).json({
      success: true,
      credits,
    });
  } catch (error) {
    next(error);
    console.error("Error fetching credits:", error);
    return res.status(500).json({ error: "Failed to fetch credits" });
  }
};

export const getPuzzleCreditById = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<Response> => {
  const { id } = req.params;
  try {
    const credit = await puzzleCredit.findById(id);
    if (!credit) {
      return res.status(404).json({ error: "Credit not found" });
    }
    return res.status(200).json({
      success: true,
      data: credit,
    });
  } catch (error) {
    next(error);
    console.error("Error fetching credit:", error);
    return res.status(500).json({ error: "Failed to fetch credit" });
  }
};

export const updatePuzzleCreditById = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<Response> => {
  const { id } = req.params;
  const { name, currency, creditAmount, amount } = req.body;

  try {
    const updatedCredit = await puzzleCredit.findByIdAndUpdate(
      id,
      { name, currency, creditAmount, amount },
      { new: true, runValidators: true }
    );

    if (!updatedCredit) {
      return res.status(404).json({ error: "Credit not found" });
    }

    return res.status(200).json({
      success: true,
      message: "Updated Successfully",
      data: updatedCredit,
    });
  } catch (error) {
    next(error);
    console.error("Error updating credit:", error);
    return res.status(500).json({ error: "Failed to update credit" });
  }
};

export const deletePuzzleCreditById = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<Response> => {
  const { id } = req.params;

  try {
    const deletedCredit = await puzzleCredit.findByIdAndDelete(id);

    if (!deletedCredit) {
      return res.status(404).json({ error: "Credit not found" });
    }

    return res.status(200).json({
      success: true,
      message: "Deleted Successfully",
    });
  } catch (error) {
    next(error);
    console.error("Error deleting credit:", error);
    return res.status(500).json({ error: "Failed to delete credit" });
  }
};
