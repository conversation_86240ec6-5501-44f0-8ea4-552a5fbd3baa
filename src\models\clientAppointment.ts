import mongoose, { Schema, Document } from 'mongoose';

interface IClientAppointment extends Document {
    userId: mongoose.Types.ObjectId;
    clientId: mongoose.Types.ObjectId;
    service: mongoose.Types.ObjectId;
    selectedServiceOptions: mongoose.Types.ObjectId[];
    date:Date;
    revenue:number;
    status:string;
}

const clientAppointmentSchema: Schema = new Schema({
    userId: { type: Schema.Types.ObjectId, ref: 'User', required: true },
    clientId: { type: mongoose.Schema.Types.ObjectId, ref: 'BusinessClient' },
    service:{ type: mongoose.Schema.Types.ObjectId, ref: 'Services' },
    selectedServiceOptions: {
        type: [mongoose.Schema.Types.ObjectId],
        default: []
    },
    date: { type: Date, required: true },
    revenue: Number,
    status: { type: String, enum: ['completed', 'canceled', 'no-show'], default: 'completed' },
},{ timestamps: true });
export default mongoose.model<IClientAppointment>('ClientAppointment', clientAppointmentSchema);
