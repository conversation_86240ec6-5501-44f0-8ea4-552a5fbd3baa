import mongoose, { Schema, Document } from 'mongoose';

interface IPaymentMethod extends Document {
  userId: mongoose.Types.ObjectId; // Reference to the user/business 
  type:'default' | 'others';
  paymentType: 'card' | 'paypal';
  provider?: string; // Provider name, e.g., 'Visa', 'Mastercard', 'PayPal'
  stripePaymethodId:string;
  last4Digits?:string;
  expiryMonth?:string;
  expiryYear?:string;
  createdAt: Date; 
  updatedAt: Date; 
}

const PaymentMethodSchema: Schema = new Schema(
  {
    userId: { type: mongoose.Types.ObjectId, required: true, ref: 'User' },
    type: {
      type: String,
      required:true,
      enum: ['default', 'others'],
      default:'others'
    },
    paymentType: {
      type: String,
      enum: ['card', 'paypal'],
      default:'card'
    },
    provider: {
      type: String,
    },
    last4Digits: {
      type: String,
    },
    expiryMonth: {
      type: String,
    },
    expiryYear: {
      type: String,
    },
    stripePaymethodId: {
        type: String,
        required: true,
    },
    
  },
  {
    timestamps: {
      createdAt: 'createdAt',
      updatedAt: 'updatedAt',
    },
  }
);

export default mongoose.model<IPaymentMethod>('PaymentMethod', PaymentMethodSchema);


